package com.archscope.app.constants;

/**
 * 服务发现相关常量
 */
public final class ServiceDiscoveryConstants {
    
    private ServiceDiscoveryConstants() {}
    
    // 分页默认值
    public static final int DEFAULT_PAGE = 0;
    public static final int DEFAULT_SIZE = 10;
    public static final int MAX_PAGE_SIZE = 100;
    
    // 排序默认值
    public static final String DEFAULT_SORT_BY = "registeredAt";
    public static final String DEFAULT_SORT_DIRECTION = "DESC";
    
    // 响应消息
    public static final String FIND_ACTIVE_SERVICES_SUCCESS = "查找活跃服务成功";
    public static final String FIND_SERVICES_SUCCESS = "分页查询服务成功";
    public static final String SEARCH_BY_NAME_SUCCESS = "根据名称搜索服务成功";
    public static final String SEARCH_BY_TYPE_SUCCESS = "根据类型查找服务成功";
    public static final String SEARCH_BY_STATUS_SUCCESS = "根据状态查找服务成功";
    public static final String SEARCH_BY_TAGS_SUCCESS = "根据标签查找服务成功";
    public static final String SEARCH_BY_CAPABILITY_SUCCESS = "根据能力查找服务成功";
    public static final String SEARCH_BY_MAVEN_SUCCESS = "根据Maven坐标查找服务成功";
    public static final String GET_SERVICE_DETAIL_SUCCESS = "获取服务详细信息成功";
    public static final String GET_STATISTICS_SUCCESS = "获取服务统计信息成功";
    public static final String SERVICE_NOT_FOUND = "服务不存在";
    public static final String NO_MATCHING_SERVICE = "未找到匹配的服务";
}