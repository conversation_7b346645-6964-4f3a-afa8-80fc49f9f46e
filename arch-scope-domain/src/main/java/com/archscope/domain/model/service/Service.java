package com.archscope.domain.model.service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

public class Service {
    private String serviceId;
    private String name;
    private String description;
    private String version;
    private String type;
    private List<String> tags;
    private String owner;
    private String apiDocUrl;
    private ServiceStatus status;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // 构造函数
    public Service(String name, String description, String version, String type, 
                  List<String> tags, String owner, String apiDocUrl) {
        this.serviceId = UUID.randomUUID().toString();
        this.name = name;
        this.description = description;
        this.version = version;
        this.type = type;
        this.tags = tags;
        this.owner = owner;
        this.apiDocUrl = apiDocUrl;
        this.status = ServiceStatus.ACTIVE;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    // Getters and setters
    public String getServiceId() {
        return serviceId;
    }

    public void setServiceId(String serviceId) {
        this.serviceId = serviceId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public List<String> getTags() {
        return tags;
    }

    public void setTags(List<String> tags) {
        this.tags = tags;
    }

    public String getOwner() {
        return owner;
    }

    public void setOwner(String owner) {
        this.owner = owner;
    }

    public String getApiDocUrl() {
        return apiDocUrl;
    }

    public void setApiDocUrl(String apiDocUrl) {
        this.apiDocUrl = apiDocUrl;
    }

    public ServiceStatus getStatus() {
        return status;
    }

    public void setStatus(ServiceStatus status) {
        this.status = status;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    // 业务方法
    public void deprecate() {
        this.status = ServiceStatus.DEPRECATED;
        this.updatedAt = LocalDateTime.now();
    }
    
    public void activate() {
        this.status = ServiceStatus.ACTIVE;
        this.updatedAt = LocalDateTime.now();
    }
    
    public void markAsDevelopment() {
        this.status = ServiceStatus.DEVELOPMENT;
        this.updatedAt = LocalDateTime.now();
    }
    
    public void update(String name, String description, String version, String type, 
                      List<String> tags, String owner, String apiDocUrl) {
        this.name = name;
        this.description = description;
        this.version = version;
        this.type = type;
        this.tags = tags;
        this.owner = owner;
        this.apiDocUrl = apiDocUrl;
        this.updatedAt = LocalDateTime.now();
    }
}