package com.archscope.domain.valueobject;

import lombok.Getter;

@Getter
public enum TaskStatus {
    PENDING("等待中"),
    PROCESSING("处理中"),  // 统一的处理中状态，适用于所有类型的任务处理
    COMPLETED("已完成"),
    FAILED("失败"),
    PARTIAL_SUCCESS("部分成功"),  // 部分成功状态
    CANCELLED("已取消"),
    WAITING("等待依赖"),
    PAUSED("已暂停");

    private final String displayName;

    TaskStatus(String displayName) {
        this.displayName = displayName;
    }

    /**
     * 检查是否为终态状态
     * @return true if the status is final (task cannot be processed further)
     */
    public boolean isFinalStatus() {
        return this == COMPLETED || this == FAILED || this == PARTIAL_SUCCESS || this == CANCELLED;
    }

    /**
     * 检查是否为可处理状态
     * @return true if the task can be picked up for processing
     */
    public boolean isProcessable() {
        return this == PENDING;
    }

    /**
     * 检查是否为活跃处理状态
     * @return true if the task is currently being processed
     */
    public boolean isActiveProcessing() {
        return this == PROCESSING;
    }

    /**
     * 向后兼容方法 - 将IN_PROGRESS映射到PROCESSING
     * @deprecated 使用PROCESSING状态替代
     */
    @Deprecated
    public static TaskStatus fromLegacyStatus(String status) {
        if ("IN_PROGRESS".equals(status)) {
            return PROCESSING;
        }
        return TaskStatus.valueOf(status);
    }
}