package com.archscope.domain.valueobject;

import java.util.Objects;
import java.util.UUID;

/**
 * 服务ID值对象，用于唯一标识一个服务
 */
public class ServiceId {
    private final String value;

    private ServiceId(String value) {
        this.value = value;
    }

    public static ServiceId of(String value) {
        return new ServiceId(value);
    }

    public static ServiceId createNew() {
        return new ServiceId(UUID.randomUUID().toString());
    }

    public String getValue() {
        return value;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ServiceId serviceId = (ServiceId) o;
        return Objects.equals(value, serviceId.value);
    }

    @Override
    public int hashCode() {
        return Objects.hash(value);
    }

    @Override
    public String toString() {
        return value;
    }
}