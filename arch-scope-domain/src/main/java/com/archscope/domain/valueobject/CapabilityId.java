package com.archscope.domain.valueobject;

import java.util.Objects;
import java.util.UUID;

/**
 * 能力ID值对象，用于唯一标识一个服务能力
 */
public class CapabilityId {
    private final String value;

    private CapabilityId(String value) {
        this.value = value;
    }

    public static CapabilityId of(String value) {
        return new CapabilityId(value);
    }

    public static CapabilityId createNew() {
        return new CapabilityId(UUID.randomUUID().toString());
    }

    public String getValue() {
        return value;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CapabilityId that = (CapabilityId) o;
        return Objects.equals(value, that.value);
    }

    @Override
    public int hashCode() {
        return Objects.hash(value);
    }

    @Override
    public String toString() {
        return value;
    }
}