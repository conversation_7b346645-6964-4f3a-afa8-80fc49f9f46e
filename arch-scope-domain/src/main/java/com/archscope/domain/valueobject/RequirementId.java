package com.archscope.domain.valueobject;

import java.util.Objects;
import java.util.UUID;

/**
 * 需求ID值对象，用于唯一标识一个需求
 */
public class RequirementId {
    private final String value;

    private RequirementId(String value) {
        this.value = value;
    }

    public static RequirementId of(String value) {
        return new RequirementId(value);
    }

    public static RequirementId createNew() {
        return new RequirementId(UUID.randomUUID().toString());
    }

    public String getValue() {
        return value;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        RequirementId that = (RequirementId) o;
        return Objects.equals(value, that.value);
    }

    @Override
    public int hashCode() {
        return Objects.hash(value);
    }

    @Override
    public String toString() {
        return value;
    }
}