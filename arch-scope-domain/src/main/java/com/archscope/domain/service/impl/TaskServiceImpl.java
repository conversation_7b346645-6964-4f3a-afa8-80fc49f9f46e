package com.archscope.domain.service.impl;

import com.archscope.domain.entity.Task;
import com.archscope.domain.repository.TaskRepository;
import com.archscope.domain.service.TaskService;
import com.archscope.domain.service.TaskQueueService;
import com.archscope.domain.valueobject.TaskStatus;
import com.archscope.domain.valueobject.TaskType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 任务服务实现类
 * 负责任务的核心业务逻辑处理
 */
@Service
public class TaskServiceImpl implements TaskService {

    private static final Logger log = LoggerFactory.getLogger(TaskServiceImpl.class);

    private final TaskRepository taskRepository;
    private final TaskQueueService taskQueueService;

    @Autowired
    public TaskServiceImpl(TaskRepository taskRepository, TaskQueueService taskQueueService) {
        this.taskRepository = taskRepository;
        this.taskQueueService = taskQueueService;
    }

    @Override
    @Transactional
    public Task createTask(Long projectId, TaskType taskType, String parameters) {
        log.info("创建任务: projectId={}, taskType={}", projectId, taskType);

        try {
            // 创建任务实体
            Task task = Task.builder()
                    .projectId(projectId)
                    .taskType(taskType.name())
                    .taskType(taskType.name())
                    .status(TaskStatus.PENDING)
                    .name("任务-" + taskType.name())
                    .description("项目 " + projectId + " 的 " + taskType.name() + " 任务")
                    .createdAt(LocalDateTime.now())
                    .updatedAt(LocalDateTime.now())
                    .retryCount(0)
                    .priority(5) // 默认优先级
                    .build();

            // 设置任务参数
            if (parameters != null && !parameters.trim().isEmpty()) {
                Map<String, Object> paramMap = new HashMap<>();
                paramMap.put("parameters", parameters);
                task.setParameters(paramMap);
            }

            // 保存任务
            Task savedTask = taskRepository.save(task);
            log.info("任务创建成功: taskId={}", savedTask.getId());

            return savedTask;
        } catch (Exception e) {
            log.error("创建任务失败: projectId={}, taskType={}, error={}", projectId, taskType, e.getMessage(), e);
            throw new RuntimeException("创建任务失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional
    public boolean submitTaskToQueue(Long taskId) {
        log.info("提交任务到队列: taskId={}", taskId);

        try {
            // 查找任务
            Task task = taskRepository.findById(taskId)
                    .orElseThrow(() -> new IllegalArgumentException("任务不存在: " + taskId));

            // 检查任务状态
            if (task.getStatus() != TaskStatus.PENDING) {
                log.warn("任务状态不是PENDING，无法提交到队列: taskId={}, status={}", taskId, task.getStatus());
                return false;
            }

            // 提交到队列
            taskQueueService.enqueueTask(task);

            log.info("任务已提交到队列: taskId={}", taskId);
            return true;

        } catch (Exception e) {
            log.error("提交任务到队列失败: taskId={}, error={}", taskId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public List<Task> getTasksByProject(Long projectId) {
        log.debug("获取项目任务列表: projectId={}", projectId);

        try {
            return taskRepository.findByProjectId(projectId);
        } catch (Exception e) {
            log.error("获取项目任务列表失败: projectId={}, error={}", projectId, e.getMessage(), e);
            throw new RuntimeException("获取项目任务列表失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional
    public boolean cancelTask(Long taskId) {
        log.info("取消任务: taskId={}", taskId);

        try {
            // 查找任务
            Task task = taskRepository.findById(taskId)
                    .orElseThrow(() -> new IllegalArgumentException("任务不存在: " + taskId));

            // 检查任务状态
            if (task.getStatus() == TaskStatus.COMPLETED || task.getStatus() == TaskStatus.FAILED) {
                log.warn("任务已完成或失败，无法取消: taskId={}, status={}", taskId, task.getStatus());
                return false;
            }

            // 更新任务状态为取消
            task.setStatus(TaskStatus.CANCELLED);
            task.setUpdatedAt(LocalDateTime.now());
            task.setCompletedAt(LocalDateTime.now());

            taskRepository.save(task);

            log.info("任务已取消: taskId={}", taskId);
            return true;

        } catch (Exception e) {
            log.error("取消任务失败: taskId={}, error={}", taskId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Task getTaskById(Long taskId) {
        log.debug("获取任务详情: taskId={}", taskId);

        try {
            return taskRepository.findById(taskId)
                    .orElseThrow(() -> new IllegalArgumentException("任务不存在: " + taskId));
        } catch (Exception e) {
            log.error("获取任务详情失败: taskId={}, error={}", taskId, e.getMessage(), e);
            throw new RuntimeException("获取任务详情失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional
    public boolean retryTask(Long taskId) {
        log.info("重试任务: taskId={}", taskId);

        try {
            // 查找任务
            Task task = taskRepository.findById(taskId)
                    .orElseThrow(() -> new IllegalArgumentException("任务不存在: " + taskId));

            // 检查任务状态
            if (task.getStatus() != TaskStatus.FAILED) {
                log.warn("只有失败的任务才能重试: taskId={}, status={}", taskId, task.getStatus());
                return false;
            }

            // 使用专门的重试方法，避免JSON序列化问题
            int updatedRows = taskRepository.retryTask(taskId);
            if (updatedRows == 0) {
                log.warn("重试任务失败，可能任务状态已改变: taskId={}", taskId);
                return false;
            }

            // 重新获取更新后的任务
            task = taskRepository.findById(taskId).orElse(null);
            if (task == null) {
                log.error("重试后无法找到任务: taskId={}", taskId);
                return false;
            }

            // 重新提交到队列
            taskQueueService.enqueueTask(task);

            log.info("任务重试成功: taskId={}, retryCount={}", taskId, task.getRetryCount());
            return true;

        } catch (Exception e) {
            log.error("重试任务失败: taskId={}, error={}", taskId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public List<Task> getAllTasks() {
        log.debug("TaskServiceImpl.getAllTasks() 开始执行");

        try {
            log.debug("调用 taskRepository.findAll()");
            List<Task> tasks = taskRepository.findAll();
            log.debug("taskRepository.findAll() 返回结果: size={}", tasks != null ? tasks.size() : "null");
            return tasks;
        } catch (Exception e) {
            log.error("获取所有任务列表失败: error={}", e.getMessage(), e);
            throw new RuntimeException("获取所有任务列表失败: " + e.getMessage(), e);
        }
    }
}