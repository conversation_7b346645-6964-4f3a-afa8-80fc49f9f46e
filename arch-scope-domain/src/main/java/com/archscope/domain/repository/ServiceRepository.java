package com.archscope.domain.repository;

import com.archscope.domain.model.servicediscovery.Service;
import com.archscope.domain.model.servicediscovery.ServiceStatus;
import com.archscope.domain.model.servicediscovery.ServiceType;
import com.archscope.domain.valueobject.ServiceId;
import com.archscope.domain.valueobject.Tag;
import com.archscope.domain.valueobject.PageResult;
import com.archscope.domain.valueobject.PageRequest;

import java.net.URL;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 服务仓储接口，定义服务实体的持久化操作
 */
public interface ServiceRepository {

    /**
     * 保存服务实体
     *
     * @param service 服务实体
     * @return 保存后的服务实体
     */
    Service save(Service service);

    /**
     * 根据ID查找服务
     *
     * @param id 服务ID
     * @return 找到的服务实体，如果不存在则返回null
     */
    Service findById(ServiceId id);

    /**
     * 根据名称查找服务
     *
     * @param name 服务名称
     * @return 找到的服务实体，如果不存在则返回null
     */
    Service findByName(String name);

    /**
     * 根据端点URL查找服务
     *
     * @param endpoint 服务端点URL
     * @return 找到的服务实体，如果不存在则返回null
     */
    Service findByEndpoint(URL endpoint);

    /**
     * 根据Maven坐标查找服务
     *
     * @param groupId Maven/Gradle坐标 - groupId
     * @param artifactId Maven/Gradle坐标 - artifactId
     * @param version 服务版本
     * @return 找到的服务实体，如果不存在则返回null
     */
    Service findByMavenCoordinates(String groupId, String artifactId, String version);

    /**
     * 根据Maven坐标查找服务列表（不指定版本）
     *
     * @param groupId Maven/Gradle坐标 - groupId
     * @param artifactId Maven/Gradle坐标 - artifactId
     * @return 找到的服务实体列表
     */
    List<Service> findByMavenCoordinatesWithoutVersion(String groupId, String artifactId);

    /**
     * 根据状态查找服务列表
     *
     * @param status 服务状态
     * @return 找到的服务实体列表
     */
    List<Service> findByStatus(ServiceStatus status);

    /**
     * 根据类型查找服务列表
     *
     * @param type 服务类型
     * @return 找到的服务实体列表
     */
    List<Service> findByType(ServiceType type);

    /**
     * 根据标签查找服务列表
     *
     * @param tags 标签集合
     * @return 找到的服务实体列表
     */
    List<Service> findByTags(Set<Tag> tags);

    /**
     * 查找所有服务
     *
     * @return 所有服务实体列表
     */
    List<Service> findAll();

    /**
     * 分页查询服务
     *
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 分页的服务实体列表
     */
    List<Service> findWithPagination(int offset, int limit);

    /**
     * 删除服务
     *
     * @param id 服务ID
     * @return 是否删除成功
     */
    boolean delete(ServiceId id);

    /**
     * 统计服务总数
     *
     * @return 服务总数
     */
    long count();

    // 新增的查询方法用于服务发现API

    /**
     * 根据ID查找服务（字符串形式）
     *
     * @param id 服务ID字符串
     * @return 找到的服务实体，如果不存在则返回null
     */
    Service findById(String id);

    /**
     * 根据名称模糊查找服务列表
     *
     * @param name 服务名称（支持模糊匹配）
     * @return 找到的服务实体列表
     */
    List<Service> findByNameContainingIgnoreCase(String name);

    /**
     * 根据状态查找服务列表（字符串形式）
     *
     * @param status 服务状态字符串
     * @return 找到的服务实体列表
     */
    List<Service> findByStatus(String status);

    /**
     * 根据类型查找服务列表（字符串形式）
     *
     * @param type 服务类型字符串
     * @return 找到的服务实体列表
     */
    List<Service> findByType(String type);

    /**
     * 根据标签查找服务列表（字符串列表形式）
     *
     * @param tags 标签字符串列表
     * @return 找到的服务实体列表
     */
    List<Service> findByTagsIn(List<String> tags);

    /**
     * 根据能力名称查找服务列表
     *
     * @param capabilityName 能力名称
     * @return 找到的服务实体列表
     */
    List<Service> findByCapabilityName(String capabilityName);

    /**
     * 根据Maven坐标查找服务列表（不包含版本）
     *
     * @param groupId Maven/Gradle坐标 - groupId
     * @param artifactId Maven/Gradle坐标 - artifactId
     * @return 找到的服务实体列表
     */
    List<Service> findByGroupIdAndArtifactId(String groupId, String artifactId);

    /**
     * 根据精确Maven坐标查找服务（包含版本）
     *
     * @param groupId Maven/Gradle坐标 - groupId
     * @param artifactId Maven/Gradle坐标 - artifactId
     * @param version 服务版本
     * @return 找到的服务实体，如果不存在则返回null
     */
    Service findByGroupIdAndArtifactIdAndVersion(String groupId, String artifactId, String version);

    /**
     * 分页查询所有服务
     *
     * @param pageRequest 分页请求
     * @return 分页的服务实体
     */
    PageResult<Service> findAll(PageRequest pageRequest);

    /**
     * 根据复杂条件查询服务
     *
     * @param name 服务名称（可选，支持模糊匹配）
     * @param type 服务类型（可选）
     * @param status 服务状态（可选）
     * @param tags 标签列表（可选）
     * @param groupId Maven坐标groupId（可选）
     * @param artifactId Maven坐标artifactId（可选）
     * @param capabilityName 能力名称（可选）
     * @param pageRequest 分页请求
     * @return 分页的服务实体
     */
    PageResult<Service> findByCriteria(String name, String type, String status, List<String> tags,
                                      String groupId, String artifactId, String capabilityName, PageRequest pageRequest);

    /**
     * 根据状态统计服务数量
     *
     * @param status 服务状态
     * @return 服务数量
     */
    long countByStatus(String status);

    /**
     * 按状态分组统计服务数量
     *
     * @return 状态统计Map
     */
    Map<String, Long> countByStatusGrouped();

    /**
     * 按类型分组统计服务数量
     *
     * @return 类型统计Map
     */
    Map<String, Long> countByTypeGrouped();

    /**
     * 统计最近几天注册的服务数量
     *
     * @param days 天数
     * @return 最近注册的服务数量
     */
    long countRecentServices(int days);
}