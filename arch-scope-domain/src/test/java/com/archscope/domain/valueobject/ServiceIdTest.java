package com.archscope.domain.valueobject;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 服务ID值对象测试
 */
class ServiceIdTest {

    @Test
    void testCreateServiceIdWithValue() {
        // Arrange
        String value = "test-service-id";

        // Act
        ServiceId serviceId = ServiceId.of(value);

        // Assert
        assertNotNull(serviceId);
        assertEquals(value, serviceId.getValue());
    }

    @Test
    void testCreateNewServiceId() {
        // Act
        ServiceId serviceId = ServiceId.createNew();

        // Assert
        assertNotNull(serviceId);
        assertNotNull(serviceId.getValue());
        assertFalse(serviceId.getValue().isEmpty());
        // UUID format validation
        assertTrue(serviceId.getValue().matches("[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}"));
    }

    @Test
    void testCreateNewServiceIdGeneratesUniqueValues() {
        // Act
        ServiceId serviceId1 = ServiceId.createNew();
        ServiceId serviceId2 = ServiceId.createNew();

        // Assert
        assertNotEquals(serviceId1.getValue(), serviceId2.getValue());
        assertNotEquals(serviceId1, serviceId2);
    }

    @Test
    void testEqualsAndHashCode() {
        // Arrange
        String value = "test-service-id";
        ServiceId serviceId1 = ServiceId.of(value);
        ServiceId serviceId2 = ServiceId.of(value);
        ServiceId serviceId3 = ServiceId.of("different-service-id");

        // Assert
        assertEquals(serviceId1, serviceId2);
        assertNotEquals(serviceId1, serviceId3);
        assertEquals(serviceId1.hashCode(), serviceId2.hashCode());
        assertNotEquals(serviceId1.hashCode(), serviceId3.hashCode());
    }

    @Test
    void testEqualsWithNull() {
        // Arrange
        ServiceId serviceId = ServiceId.of("test-service-id");

        // Assert
        assertNotEquals(serviceId, null);
    }

    @Test
    void testEqualsWithDifferentType() {
        // Arrange
        ServiceId serviceId = ServiceId.of("test-service-id");
        String string = "test-service-id";

        // Assert
        assertNotEquals(serviceId, string);
    }

    @Test
    void testEqualsWithSameInstance() {
        // Arrange
        ServiceId serviceId = ServiceId.of("test-service-id");

        // Assert
        assertEquals(serviceId, serviceId);
    }

    @Test
    void testToString() {
        // Arrange
        String value = "test-service-id";
        ServiceId serviceId = ServiceId.of(value);

        // Act
        String toString = serviceId.toString();

        // Assert
        assertEquals(value, toString);
    }

    @Test
    void testCreateWithNullValue() {
        // Act & Assert
        assertDoesNotThrow(() -> ServiceId.of(null));
        ServiceId serviceId = ServiceId.of(null);
        assertNull(serviceId.getValue());
    }

    @Test
    void testCreateWithEmptyValue() {
        // Arrange
        String emptyValue = "";

        // Act
        ServiceId serviceId = ServiceId.of(emptyValue);

        // Assert
        assertEquals(emptyValue, serviceId.getValue());
    }
}