openapi: 3.0.3
info:
  title: ArchScope 服务发现系统 API
  description: |
    ArchScope服务发现系统提供了完整的服务注册、发现、能力管理和需求匹配功能。
    
    ## 主要功能
    - **服务注册管理**: 注册、更新、删除服务
    - **服务发现**: 多种条件查询和过滤服务
    - **能力管理**: 管理服务提供的能力和示例
    - **需求匹配**: 基于需求智能匹配合适的服务
    - **反馈系统**: 收集和处理用户反馈以改进推荐
    
    ## 认证方式
    - JWT Bearer Token
    - API Key
  version: 1.0.0
  contact:
    name: ArchScope团队
    email: <EMAIL>
    url: https://www.archscope.com
  license:
    name: MIT License
    url: https://opensource.org/licenses/MIT

servers:
  - url: http://localhost:8080/api/v1
    description: 开发环境
  - url: https://api.archscope.com/api/v1
    description: 生产环境

security:
  - bearerAuth: []
  - apiKey: []

tags:
  - name: 服务注册管理
    description: 服务注册、更新、删除等管理操作
  - name: 服务发现
    description: 服务查询和发现的REST API
  - name: 能力管理
    description: 服务能力注册、查询和管理相关操作
  - name: 需求匹配
    description: 需求匹配和服务推荐相关操作

paths:
  /services:
    post:
      tags:
        - 服务注册管理
      summary: 注册服务
      description: 注册一个新的服务到服务注册中心
      operationId: registerService
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ServiceRegistrationCommand'
            examples:
              userService:
                summary: 用户管理服务注册示例
                value:
                  name: 用户管理服务
                  description: 提供用户注册、登录、权限管理等功能的微服务
                  type: REST_API
                  version: 1.0.0
                  endpoint: https://api.example.com/user-service
                  groupId: com.example
                  artifactId: user-service
                  tags: [authentication, user-management, microservice]
                  status: ACTIVE
                  metadata:
                    environment: production
                    region: us-east-1
                    maintainer: <EMAIL>
      responses:
        '201':
          description: 服务注册成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponseServiceDTO'
              examples:
                success:
                  summary: 注册成功响应
                  value:
                    success: true
                    message: 服务注册成功
                    data:
                      id: srv-001
                      name: 用户管理服务
                      description: 提供用户注册、登录、权限管理等功能
                      type: REST_API
                      version: 1.0.0
                      endpoint: https://api.example.com/user-service
                      status: ACTIVE
                      registeredAt: 2024-01-20T15:30:00Z
                    timestamp: 2024-01-20T15:30:00Z
        '400':
          $ref: '#/components/responses/BadRequest'
        '409':
          $ref: '#/components/responses/Conflict'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /services/{serviceId}:
    get:
      tags:
        - 服务注册管理
      summary: 获取服务详情
      description: 根据服务ID获取服务的详细信息
      operationId: getServiceById
      parameters:
        - name: serviceId
          in: path
          required: true
          description: 服务ID
          schema:
            type: string
            example: srv-001
      responses:
        '200':
          description: 获取服务详情成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponseServiceDTO'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

    put:
      tags:
        - 服务注册管理
      summary: 更新服务信息
      description: 更新已注册服务的信息
      operationId: updateService
      parameters:
        - name: serviceId
          in: path
          required: true
          description: 服务ID
          schema:
            type: string
            example: srv-001
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ServiceUpdateCommand'
      responses:
        '200':
          description: 服务更新成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponseServiceDTO'
        '400':
          $ref: '#/components/responses/BadRequest'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

    delete:
      tags:
        - 服务注册管理
      summary: 注销服务
      description: 从服务注册中心注销指定服务
      operationId: deregisterService
      parameters:
        - name: serviceId
          in: path
          required: true
          description: 服务ID
          schema:
            type: string
            example: srv-001
      responses:
        '200':
          description: 服务注销成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponseVoid'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /discovery/active:
    get:
      tags:
        - 服务发现
      summary: 查找所有活跃服务
      description: 获取系统中所有状态为活跃的服务列表
      operationId: findAllActiveServices
      responses:
        '200':
          description: 成功获取活跃服务列表
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponseServiceDTOList'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /discovery:
    get:
      tags:
        - 服务发现
      summary: 分页查询服务
      description: 支持分页和排序的服务查询接口
      operationId: findServices
      parameters:
        - name: page
          in: query
          description: 页码，从0开始
          schema:
            type: integer
            minimum: 0
            default: 0
            example: 0
        - name: size
          in: query
          description: 每页大小，最大100
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 10
            example: 10
        - name: sortBy
          in: query
          description: 排序字段
          schema:
            type: string
            enum: [name, type, status, registeredAt, lastUpdatedAt]
            default: registeredAt
            example: registeredAt
        - name: sortDirection
          in: query
          description: 排序方向
          schema:
            type: string
            enum: [ASC, DESC]
            default: DESC
            example: DESC
      responses:
        '200':
          description: 成功获取分页服务列表
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponsePageServiceDTO'
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /discovery/search:
    get:
      tags:
        - 服务发现
      summary: 组合条件查询服务
      description: 支持多种条件的组合查询
      operationId: findServicesByCriteria
      parameters:
        - name: name
          in: query
          description: 服务名称（支持模糊匹配）
          schema:
            type: string
            example: 用户管理
        - name: type
          in: query
          description: 服务类型
          schema:
            type: string
            enum: [REST_API, GRAPHQL_API, GRPC_SERVICE, MESSAGE_QUEUE, DATABASE, CACHE, OTHER]
            example: REST_API
        - name: status
          in: query
          description: 服务状态
          schema:
            type: string
            enum: [ACTIVE, INACTIVE, DEPRECATED, MAINTENANCE, UNKNOWN]
            example: ACTIVE
        - name: tags
          in: query
          description: 标签列表
          schema:
            type: array
            items:
              type: string
            example: [authentication, user-management]
        - name: groupId
          in: query
          description: Maven坐标groupId
          schema:
            type: string
            example: com.example
        - name: artifactId
          in: query
          description: Maven坐标artifactId
          schema:
            type: string
            example: user-service
        - name: capabilityName
          in: query
          description: 能力名称
          schema:
            type: string
            example: 用户认证
        - name: page
          in: query
          description: 页码
          schema:
            type: integer
            minimum: 0
            default: 0
        - name: size
          in: query
          description: 每页大小
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 10
        - name: sortBy
          in: query
          description: 排序字段
          schema:
            type: string
            default: registeredAt
        - name: sortDirection
          in: query
          description: 排序方向
          schema:
            type: string
            enum: [ASC, DESC]
            default: DESC
      responses:
        '200':
          description: 组合条件查询服务成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponsePageServiceDTO'
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /capabilities:
    post:
      tags:
        - 能力管理
      summary: 注册服务能力
      description: 为服务注册新的能力
      operationId: registerCapability
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CapabilityRegistrationCommand'
            examples:
              authCapability:
                summary: 用户认证能力注册示例
                value:
                  name: 用户认证
                  description: 提供用户登录认证功能，支持用户名密码和第三方OAuth认证
                  version: 1.0.0
                  examples:
                    - name: 用户名密码登录
                      description: 使用用户名和密码进行登录认证
                      requestExample: '{"username": "john", "password": "secret"}'
                      responseExample: '{"token": "jwt-token", "userId": "123", "expiresIn": 3600}'
                  tags: [authentication, security, login]
      responses:
        '201':
          description: 能力注册成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponseCapabilityDTO'
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /requirements/match:
    post:
      tags:
        - 需求匹配
      summary: 根据需求匹配服务
      description: 根据需求描述和能力要求匹配合适的服务
      operationId: matchServices
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RequirementMatchingCommand'
            examples:
              userManagement:
                summary: 用户管理需求匹配示例
                value:
                  description: 我需要一个用户管理系统，支持用户注册、登录、权限管理等功能
                  requiredCapabilities: [用户认证, 权限管理, 用户注册]
                  priority: HIGH
                  tags: [authentication, user-management]
      responses:
        '200':
          description: 需求匹配成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponseServiceRecommendationDTOList'
              examples:
                matchResult:
                  summary: 匹配结果示例
                  value:
                    success: true
                    message: 需求匹配成功
                    data:
                      - serviceId: srv-001
                        serviceName: 用户管理服务
                        matchScore: 0.95
                        matchReason: 完全匹配用户认证和权限管理需求
                        recommendationId: rec-001
                        capabilities: [用户认证, 权限管理]
                        confidence: HIGH
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalServerError'

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT认证令牌
    apiKey:
      type: apiKey
      in: header
      name: X-API-Key
      description: API密钥认证

  schemas:
    ServiceRegistrationCommand:
      type: object
      required:
        - name
        - type
        - version
        - endpoint
      properties:
        name:
          type: string
          minLength: 2
          maxLength: 100
          description: 服务名称
          example: 用户管理服务
        description:
          type: string
          maxLength: 500
          description: 服务详细描述
          example: 提供用户注册、登录、权限管理等功能的微服务
        type:
          type: string
          enum: [REST_API, GRAPHQL_API, GRPC_SERVICE, MESSAGE_QUEUE, DATABASE, CACHE, OTHER]
          description: 服务类型
          example: REST_API
        version:
          type: string
          pattern: '^\\d+\\.\\d+\\.\\d+(-[a-zA-Z0-9]+)?$'
          description: 服务版本号（语义化版本）
          example: 1.0.0
        endpoint:
          type: string
          format: uri
          description: 服务访问端点URL
          example: https://api.example.com/user-service
        groupId:
          type: string
          pattern: '^[a-zA-Z][a-zA-Z0-9_]*(?:\\.[a-zA-Z][a-zA-Z0-9_]*)*$'
          description: Maven/Gradle坐标的groupId
          example: com.example
        artifactId:
          type: string
          pattern: '^[a-zA-Z][a-zA-Z0-9_-]*$'
          description: Maven/Gradle坐标的artifactId
          example: user-service
        tags:
          type: array
          maxItems: 20
          items:
            type: string
            maxLength: 50
          description: 服务标签列表
          example: [authentication, user-management, microservice]
        status:
          type: string
          enum: [ACTIVE, INACTIVE, DEPRECATED, MAINTENANCE, UNKNOWN]
          default: ACTIVE
          description: 服务初始状态
          example: ACTIVE
        metadata:
          type: object
          maxProperties: 50
          additionalProperties:
            type: string
            maxLength: 500
          description: 服务元数据键值对
          example:
            environment: production
            region: us-east-1
            maintainer: <EMAIL>

    ServiceUpdateCommand:
      type: object
      properties:
        name:
          type: string
          minLength: 2
          maxLength: 100
          description: 服务名称
        description:
          type: string
          maxLength: 500
          description: 服务详细描述
        endpoint:
          type: string
          format: uri
          description: 服务访问端点URL
        tags:
          type: array
          maxItems: 20
          items:
            type: string
            maxLength: 50
          description: 服务标签列表
        status:
          type: string
          enum: [ACTIVE, INACTIVE, DEPRECATED, MAINTENANCE, UNKNOWN]
          description: 服务状态
        metadata:
          type: object
          maxProperties: 50
          additionalProperties:
            type: string
            maxLength: 500
          description: 服务元数据键值对

    CapabilityRegistrationCommand:
      type: object
      required:
        - name
        - version
      properties:
        name:
          type: string
          minLength: 2
          maxLength: 100
          description: 能力名称
          example: 用户认证
        description:
          type: string
          maxLength: 500
          description: 能力详细描述
          example: 提供用户登录认证功能，支持用户名密码和第三方OAuth认证
        version:
          type: string
          pattern: '^\\d+\\.\\d+\\.\\d+(-[a-zA-Z0-9]+)?$'
          description: 能力版本号（语义化版本）
          example: 1.0.0
        examples:
          type: array
          maxItems: 10
          items:
            $ref: '#/components/schemas/CapabilityExampleCommand'
          description: 能力使用示例列表
        tags:
          type: array
          maxItems: 20
          items:
            type: string
            maxLength: 50
          description: 能力标签列表
          example: [authentication, security, login]

    CapabilityExampleCommand:
      type: object
      required:
        - name
      properties:
        name:
          type: string
          minLength: 2
          maxLength: 100
          description: 示例名称
          example: 用户名密码登录
        description:
          type: string
          maxLength: 300
          description: 示例详细描述
          example: 使用用户名和密码进行登录认证
        requestExample:
          type: string
          maxLength: 2000
          description: 请求示例（JSON格式）
          example: '{"username": "john", "password": "secret"}'
        responseExample:
          type: string
          maxLength: 2000
          description: 响应示例（JSON格式）
          example: '{"token": "jwt-token", "userId": "123", "expiresIn": 3600}'

    RequirementMatchingCommand:
      type: object
      required:
        - description
      properties:
        description:
          type: string
          minLength: 10
          maxLength: 1000
          description: 需求描述
          example: 我需要一个用户管理系统，支持用户注册、登录、权限管理等功能
        requiredCapabilities:
          type: array
          items:
            type: string
          description: 需要的能力列表
          example: [用户认证, 权限管理, 用户注册]
        priority:
          type: string
          enum: [LOW, MEDIUM, HIGH, CRITICAL]
          description: 需求优先级
          example: HIGH
        tags:
          type: array
          items:
            type: string
          description: 需求标签
          example: [authentication, user-management]

    ServiceDTO:
      type: object
      properties:
        id:
          type: string
          description: 服务唯一标识符
          example: srv-001
        name:
          type: string
          description: 服务名称
          example: 用户管理服务
        description:
          type: string
          description: 服务详细描述
          example: 提供用户注册、登录、权限管理等功能的微服务
        type:
          type: string
          enum: [REST_API, GRAPHQL_API, GRPC_SERVICE, MESSAGE_QUEUE, DATABASE, CACHE, OTHER]
          description: 服务类型
          example: REST_API
        version:
          type: string
          description: 服务版本号
          example: 1.2.0
        endpoint:
          type: string
          format: uri
          description: 服务访问端点URL
          example: https://api.example.com/user-service
        groupId:
          type: string
          description: Maven/Gradle坐标的groupId
          example: com.example
        artifactId:
          type: string
          description: Maven/Gradle坐标的artifactId
          example: user-service
        tags:
          type: array
          items:
            type: string
          description: 服务标签列表
          example: [authentication, user-management, microservice]
        status:
          type: string
          enum: [ACTIVE, INACTIVE, DEPRECATED, MAINTENANCE, UNKNOWN]
          description: 服务当前状态
          example: ACTIVE
        metadata:
          type: object
          additionalProperties:
            type: string
          description: 服务元数据键值对
          example:
            environment: production
            region: us-east-1
        registeredAt:
          type: string
          format: date-time
          description: 服务注册时间
          example: 2024-01-15T10:30:00Z
        lastUpdatedAt:
          type: string
          format: date-time
          description: 服务最后更新时间
          example: 2024-01-20T14:45:00Z
        capabilities:
          type: array
          items:
            $ref: '#/components/schemas/CapabilityDTO'
          description: 服务提供的能力列表

    CapabilityDTO:
      type: object
      properties:
        id:
          type: string
          description: 能力唯一标识符
          example: cap-001
        name:
          type: string
          description: 能力名称
          example: 用户认证
        description:
          type: string
          description: 能力详细描述
          example: 提供用户登录认证功能
        version:
          type: string
          description: 能力版本号
          example: 1.0.0
        examples:
          type: array
          items:
            $ref: '#/components/schemas/CapabilityExampleDTO'
          description: 能力使用示例列表
        deprecated:
          type: boolean
          description: 是否已废弃
          example: false
        createdAt:
          type: string
          format: date-time
          description: 能力创建时间
          example: 2024-01-20T15:30:00Z

    CapabilityExampleDTO:
      type: object
      properties:
        id:
          type: string
          description: 示例唯一标识符
          example: ex-001
        name:
          type: string
          description: 示例名称
          example: 用户名密码登录
        description:
          type: string
          description: 示例详细描述
          example: 使用用户名和密码进行登录认证
        requestExample:
          type: string
          description: 请求示例
          example: '{"username": "john", "password": "secret"}'
        responseExample:
          type: string
          description: 响应示例
          example: '{"token": "jwt-token", "userId": "123"}'

    ServiceRecommendationDTO:
      type: object
      properties:
        serviceId:
          type: string
          description: 服务ID
          example: srv-001
        serviceName:
          type: string
          description: 服务名称
          example: 用户管理服务
        matchScore:
          type: number
          format: double
          minimum: 0
          maximum: 1
          description: 匹配分数
          example: 0.95
        matchReason:
          type: string
          description: 匹配原因
          example: 完全匹配用户认证和权限管理需求
        recommendationId:
          type: string
          description: 推荐ID
          example: rec-001
        capabilities:
          type: array
          items:
            type: string
          description: 匹配的能力列表
          example: [用户认证, 权限管理]
        confidence:
          type: string
          enum: [LOW, MEDIUM, HIGH]
          description: 推荐置信度
          example: HIGH

    PageResponseDTO:
      type: object
      properties:
        content:
          type: array
          items:
            type: object
          description: 页面内容
        page:
          type: integer
          description: 当前页码
          example: 0
        size:
          type: integer
          description: 每页大小
          example: 10
        totalElements:
          type: integer
          format: int64
          description: 总元素数
          example: 25
        totalPages:
          type: integer
          description: 总页数
          example: 3
        first:
          type: boolean
          description: 是否为第一页
          example: true
        last:
          type: boolean
          description: 是否为最后一页
          example: false

    ApiResponse:
      type: object
      properties:
        success:
          type: boolean
          description: 操作是否成功
          example: true
        message:
          type: string
          description: 响应消息
          example: 操作成功
        data:
          type: object
          description: 响应数据
        error:
          type: string
          description: 错误信息
        timestamp:
          type: string
          format: date-time
          description: 响应时间戳
          example: 2024-01-20T15:30:00Z

    ApiResponseServiceDTO:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/ServiceDTO'

    ApiResponseServiceDTOList:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data:
              type: array
              items:
                $ref: '#/components/schemas/ServiceDTO'

    ApiResponsePageServiceDTO:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data:
              allOf:
                - $ref: '#/components/schemas/PageResponseDTO'
                - type: object
                  properties:
                    content:
                      type: array
                      items:
                        $ref: '#/components/schemas/ServiceDTO'

    ApiResponseCapabilityDTO:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/CapabilityDTO'

    ApiResponseServiceRecommendationDTOList:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data:
              type: array
              items:
                $ref: '#/components/schemas/ServiceRecommendationDTO'

    ApiResponseVoid:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data:
              type: object
              nullable: true

  responses:
    BadRequest:
      description: 请求参数错误
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ApiResponse'
          examples:
            parameterError:
              summary: 参数错误响应
              value:
                success: false
                message: 请求参数错误
                error: 服务名称不能为空
                timestamp: 2024-01-20T15:30:00Z

    NotFound:
      description: 资源不存在
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ApiResponse'
          examples:
            notFound:
              summary: 资源不存在响应
              value:
                success: false
                message: 资源不存在
                error: 指定的服务不存在
                timestamp: 2024-01-20T15:30:00Z

    Conflict:
      description: 资源冲突
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ApiResponse'
          examples:
            conflict:
              summary: 资源冲突响应
              value:
                success: false
                message: 服务注册失败
                error: 相同名称和版本的服务已存在
                timestamp: 2024-01-20T15:30:00Z

    InternalServerError:
      description: 服务器内部错误
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ApiResponse'
          examples:
            serverError:
              summary: 服务器错误响应
              value:
                success: false
                message: 服务器内部错误
                error: 数据库连接异常
                timestamp: 2024-01-20T15:30:00Z