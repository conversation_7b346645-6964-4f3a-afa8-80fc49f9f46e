package com.archscope.config;

import io.swagger.v3.oas.models.examples.Example;
import io.swagger.v3.oas.models.media.Content;
import io.swagger.v3.oas.models.media.MediaType;
import io.swagger.v3.oas.models.responses.ApiResponse;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

/**
 * API示例配置类
 * 提供详细的API请求和响应示例
 */
@Configuration
public class ApiExamplesConfig {

    /**
     * 服务注册请求示例
     */
    @Bean
    public Map<String, Example> serviceRegistrationExamples() {
        Map<String, Example> examples = new HashMap<>();
        
        // 用户管理服务示例
        Example userServiceExample = new Example();
        userServiceExample.setSummary("用户管理服务注册");
        userServiceExample.setDescription("注册一个提供用户管理功能的REST API服务");
        userServiceExample.setValue("""
            {
                "name": "用户管理服务",
                "description": "提供用户注册、登录、权限管理等功能的微服务",
                "type": "REST_API",
                "version": "1.0.0",
                "endpoint": "https://api.example.com/user-service",
                "groupId": "com.example",
                "artifactId": "user-service",
                "tags": ["authentication", "user-management", "microservice"],
                "status": "ACTIVE",
                "metadata": {
                    "environment": "production",
                    "region": "us-east-1",
                    "maintainer": "<EMAIL>",
                    "documentation": "https://docs.example.com/user-service"
                }
            }
            """);
        examples.put("userService", userServiceExample);
        
        // 支付服务示例
        Example paymentServiceExample = new Example();
        paymentServiceExample.setSummary("支付服务注册");
        paymentServiceExample.setDescription("注册一个处理支付业务的服务");
        paymentServiceExample.setValue("""
            {
                "name": "支付处理服务",
                "description": "处理各种支付方式的核心服务，支持支付宝、微信、银行卡等",
                "type": "REST_API",
                "version": "2.1.0",
                "endpoint": "https://payment.example.com/api/v2",
                "groupId": "com.example.payment",
                "artifactId": "payment-service",
                "tags": ["payment", "financial", "transaction"],
                "status": "ACTIVE",
                "metadata": {
                    "environment": "production",
                    "region": "cn-north-1",
                    "maintainer": "<EMAIL>",
                    "sla": "99.9%",
                    "rateLimit": "1000/min"
                }
            }
            """);
        examples.put("paymentService", paymentServiceExample);
        
        // 消息队列服务示例
        Example messageQueueExample = new Example();
        messageQueueExample.setSummary("消息队列服务注册");
        messageQueueExample.setDescription("注册一个消息队列服务");
        messageQueueExample.setValue("""
            {
                "name": "订单消息队列",
                "description": "处理订单相关消息的RocketMQ队列服务",
                "type": "MESSAGE_QUEUE",
                "version": "1.0.0",
                "endpoint": "rocketmq://mq.example.com:9876",
                "groupId": "com.example.mq",
                "artifactId": "order-queue",
                "tags": ["message-queue", "order", "async"],
                "status": "ACTIVE",
                "metadata": {
                    "queueType": "RocketMQ",
                    "topics": "order-created,order-updated,order-cancelled",
                    "maxMessageSize": "4MB"
                }
            }
            """);
        examples.put("messageQueue", messageQueueExample);
        
        return examples;
    }

    /**
     * 能力注册请求示例
     */
    @Bean
    public Map<String, Example> capabilityRegistrationExamples() {
        Map<String, Example> examples = new HashMap<>();
        
        // 用户认证能力示例
        Example authCapabilityExample = new Example();
        authCapabilityExample.setSummary("用户认证能力");
        authCapabilityExample.setDescription("用户登录认证功能的能力注册");
        authCapabilityExample.setValue("""
            {
                "name": "用户认证",
                "description": "提供用户登录认证功能，支持用户名密码和第三方OAuth认证",
                "version": "1.0.0",
                "examples": [
                    {
                        "name": "用户名密码登录",
                        "description": "使用用户名和密码进行登录认证",
                        "requestExample": "{\\"username\\": \\"john\\", \\"password\\": \\"secret\\"}",
                        "responseExample": "{\\"token\\": \\"jwt-token\\", \\"userId\\": \\"123\\", \\"expiresIn\\": 3600}"
                    },
                    {
                        "name": "OAuth登录",
                        "description": "使用第三方OAuth进行登录认证",
                        "requestExample": "{\\"provider\\": \\"google\\", \\"authCode\\": \\"auth-code\\"}",
                        "responseExample": "{\\"token\\": \\"jwt-token\\", \\"userId\\": \\"456\\", \\"profile\\": {...}}"
                    }
                ],
                "tags": ["authentication", "security", "login"]
            }
            """);
        examples.put("authCapability", authCapabilityExample);
        
        // 支付处理能力示例
        Example paymentCapabilityExample = new Example();
        paymentCapabilityExample.setSummary("支付处理能力");
        paymentCapabilityExample.setDescription("支付处理功能的能力注册");
        paymentCapabilityExample.setValue("""
            {
                "name": "在线支付",
                "description": "处理各种在线支付方式，包括支付宝、微信支付、银行卡支付",
                "version": "2.0.0",
                "examples": [
                    {
                        "name": "支付宝支付",
                        "description": "使用支付宝进行在线支付",
                        "requestExample": "{\\"amount\\": 100.00, \\"currency\\": \\"CNY\\", \\"method\\": \\"alipay\\", \\"orderId\\": \\"order-123\\"}",
                        "responseExample": "{\\"paymentId\\": \\"pay-456\\", \\"status\\": \\"pending\\", \\"payUrl\\": \\"https://pay.alipay.com/...\\", \\"qrCode\\": \\"data:image/png;base64,...\\"}"
                    },
                    {
                        "name": "微信支付",
                        "description": "使用微信支付进行在线支付",
                        "requestExample": "{\\"amount\\": 50.00, \\"currency\\": \\"CNY\\", \\"method\\": \\"wechat\\", \\"orderId\\": \\"order-124\\"}",
                        "responseExample": "{\\"paymentId\\": \\"pay-457\\", \\"status\\": \\"pending\\", \\"prepayId\\": \\"wx-prepay-id\\", \\"qrCode\\": \\"weixin://wxpay/...\\"}",
                    }
                ],
                "tags": ["payment", "financial", "online-payment"]
            }
            """);
        examples.put("paymentCapability", paymentCapabilityExample);
        
        return examples;
    }

    /**
     * 需求匹配请求示例
     */
    @Bean
    public Map<String, Example> requirementMatchingExamples() {
        Map<String, Example> examples = new HashMap<>();
        
        // 电商系统需求示例
        Example ecommerceRequirementExample = new Example();
        ecommerceRequirementExample.setSummary("电商系统需求");
        ecommerceRequirementExample.setDescription("构建电商系统的需求匹配");
        ecommerceRequirementExample.setValue("""
            {
                "description": "我需要构建一个电商系统，需要用户管理、商品管理、订单处理、支付功能和库存管理",
                "requiredCapabilities": [
                    "用户认证",
                    "用户权限管理",
                    "商品管理",
                    "订单处理",
                    "在线支付",
                    "库存管理",
                    "消息通知"
                ],
                "priority": "HIGH",
                "tags": ["ecommerce", "online-shopping", "retail"],
                "constraints": {
                    "maxResponseTime": "200ms",
                    "availability": "99.9%",
                    "region": "cn-north-1"
                }
            }
            """);
        examples.put("ecommerceRequirement", ecommerceRequirementExample);
        
        // 内容管理系统需求示例
        Example cmsRequirementExample = new Example();
        cmsRequirementExample.setSummary("内容管理系统需求");
        cmsRequirementExample.setDescription("构建内容管理系统的需求匹配");
        cmsRequirementExample.setValue("""
            {
                "description": "需要一个内容管理系统，支持文章发布、用户评论、文件上传、搜索功能",
                "requiredCapabilities": [
                    "内容管理",
                    "文件上传",
                    "全文搜索",
                    "用户评论",
                    "权限控制"
                ],
                "priority": "MEDIUM",
                "tags": ["cms", "content", "publishing"],
                "constraints": {
                    "storageType": "cloud",
                    "searchEngine": "elasticsearch"
                }
            }
            """);
        examples.put("cmsRequirement", cmsRequirementExample);
        
        return examples;
    }

    /**
     * 成功响应示例
     */
    @Bean
    public Map<String, Example> successResponseExamples() {
        Map<String, Example> examples = new HashMap<>();
        
        // 服务注册成功响应
        Example serviceRegistrationSuccess = new Example();
        serviceRegistrationSuccess.setSummary("服务注册成功");
        serviceRegistrationSuccess.setValue("""
            {
                "success": true,
                "message": "服务注册成功",
                "data": {
                    "id": "srv-001",
                    "name": "用户管理服务",
                    "description": "提供用户注册、登录、权限管理等功能",
                    "type": "REST_API",
                    "version": "1.0.0",
                    "endpoint": "https://api.example.com/user-service",
                    "groupId": "com.example",
                    "artifactId": "user-service",
                    "tags": ["authentication", "user-management", "microservice"],
                    "status": "ACTIVE",
                    "registeredAt": "2024-01-20T15:30:00Z",
                    "lastUpdatedAt": "2024-01-20T15:30:00Z",
                    "capabilities": []
                },
                "timestamp": "2024-01-20T15:30:00Z"
            }
            """);
        examples.put("serviceRegistrationSuccess", serviceRegistrationSuccess);
        
        // 需求匹配成功响应
        Example requirementMatchingSuccess = new Example();
        requirementMatchingSuccess.setSummary("需求匹配成功");
        requirementMatchingSuccess.setValue("""
            {
                "success": true,
                "message": "需求匹配成功",
                "data": [
                    {
                        "serviceId": "srv-001",
                        "serviceName": "用户管理服务",
                        "matchScore": 0.95,
                        "matchReason": "完全匹配用户认证和权限管理需求，服务稳定性高",
                        "recommendationId": "rec-001",
                        "capabilities": ["用户认证", "权限管理", "用户注册"],
                        "confidence": "HIGH",
                        "serviceDetails": {
                            "version": "1.2.0",
                            "endpoint": "https://api.example.com/user-service",
                            "status": "ACTIVE",
                            "sla": "99.9%"
                        }
                    },
                    {
                        "serviceId": "srv-002",
                        "serviceName": "支付处理服务",
                        "matchScore": 0.88,
                        "matchReason": "提供完整的支付功能，支持多种支付方式",
                        "recommendationId": "rec-002",
                        "capabilities": ["在线支付", "支付回调", "退款处理"],
                        "confidence": "HIGH",
                        "serviceDetails": {
                            "version": "2.1.0",
                            "endpoint": "https://payment.example.com/api/v2",
                            "status": "ACTIVE",
                            "sla": "99.95%"
                        }
                    }
                ],
                "timestamp": "2024-01-20T15:30:00Z"
            }
            """);
        examples.put("requirementMatchingSuccess", requirementMatchingSuccess);
        
        return examples;
    }

    /**
     * 错误响应示例
     */
    @Bean
    public Map<String, Example> errorResponseExamples() {
        Map<String, Example> examples = new HashMap<>();
        
        // 参数验证错误
        Example validationError = new Example();
        validationError.setSummary("参数验证错误");
        validationError.setValue("""
            {
                "success": false,
                "message": "请求参数错误",
                "error": "服务名称不能为空，版本号格式不正确",
                "details": {
                    "fieldErrors": [
                        {
                            "field": "name",
                            "message": "服务名称不能为空"
                        },
                        {
                            "field": "version",
                            "message": "版本号格式不正确，应使用语义化版本格式"
                        }
                    ]
                },
                "timestamp": "2024-01-20T15:30:00Z"
            }
            """);
        examples.put("validationError", validationError);
        
        // 资源不存在错误
        Example notFoundError = new Example();
        notFoundError.setSummary("资源不存在");
        notFoundError.setValue("""
            {
                "success": false,
                "message": "资源不存在",
                "error": "指定的服务ID不存在",
                "details": {
                    "resourceType": "Service",
                    "resourceId": "srv-999",
                    "suggestion": "请检查服务ID是否正确，或使用服务发现接口查询可用服务"
                },
                "timestamp": "2024-01-20T15:30:00Z"
            }
            """);
        examples.put("notFoundError", notFoundError);
        
        // 服务冲突错误
        Example conflictError = new Example();
        conflictError.setSummary("服务冲突");
        conflictError.setValue("""
            {
                "success": false,
                "message": "服务注册失败",
                "error": "相同名称和版本的服务已存在",
                "details": {
                    "conflictType": "SERVICE_DUPLICATE",
                    "existingServiceId": "srv-001",
                    "conflictFields": ["name", "version"],
                    "suggestion": "请使用不同的服务名称或版本号，或者更新现有服务"
                },
                "timestamp": "2024-01-20T15:30:00Z"
            }
            """);
        examples.put("conflictError", conflictError);
        
        return examples;
    }
}