package com.archscope.config;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.tags.Tag;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;

/**
 * OpenAPI配置类
 */
@Configuration
public class OpenApiConfig {

    /**
     * 配置OpenAPI
     * @return OpenAPI实例
     */
    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("ArchScope API文档")
                        .description("ArchScope是一个面向开发者的架构观测和守护系统，旨在通过自动化分析" +
                                "设计文档和代码仓库，为开发者提供项目的全局视角，帮助开发者快速理解和治理项目。\n\n" +
                                "## 主要功能模块\n" +
                                "- **Git仓库管理**: 验证Git仓库、获取仓库详细信息、提取项目元数据\n" +
                                "- **项目管理**: 项目注册、配置管理、状态跟踪\n" +
                                "- **任务管理**: 代码分析任务、文档生成任务的创建和监控\n" +
                                "- **文档管理**: 自动生成项目文档、版本管理、静态站点生成\n\n" +
                                "## 快速开始\n" +
                                "1. 使用Git仓库验证接口验证您的代码仓库\n" +
                                "2. 注册项目并配置分析选项\n" +
                                "3. 查看生成的文档和分析结果")
                        .version("1.0.0")
                        .contact(new Contact()
                                .name("ArchScope团队")
                                .email("<EMAIL>")
                                .url("https://www.archscope.com"))
                        .license(new License()
                                .name("MIT License")
                                .url("https://opensource.org/licenses/MIT")))

                .tags(Arrays.asList(
                        new Tag().name("Git仓库管理")
                                .description("Git仓库验证、信息获取和元数据提取相关接口。支持GitHub、GitLab、Bitbucket等主流Git托管平台。"),
                        new Tag().name("项目管理")
                                .description("项目注册、配置管理、状态查询等核心功能接口。"),
                        new Tag().name("任务管理")
                                .description("代码分析任务、文档生成任务的创建、监控和管理接口。"),
                        new Tag().name("文档管理")
                                .description("项目文档的生成、版本管理和静态站点生成接口。"),
                        new Tag().name("系统管理")
                                .description("系统状态、配置管理和监控相关接口。")
                ));
    }
} 