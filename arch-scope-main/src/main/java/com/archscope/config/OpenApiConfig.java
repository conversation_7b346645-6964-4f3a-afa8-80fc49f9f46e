package com.archscope.config;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.tags.Tag;
import io.swagger.v3.oas.models.servers.Server;
import io.swagger.v3.oas.models.ExternalDocumentation;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;

/**
 * OpenAPI配置类
 * 配置Swagger/OpenAPI文档生成
 */
@Configuration
public class OpenApiConfig {

    /**
     * 配置OpenAPI
     * @return OpenAPI实例
     */
    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("ArchScope API文档")
                        .description("ArchScope是一个面向开发者的架构观测和守护系统，旨在通过自动化分析" +
                                "设计文档和代码仓库，为开发者提供项目的全局视角，帮助开发者快速理解和治理项目。\n\n" +
                                "## 主要功能模块\n" +
                                "- **Git仓库管理**: 验证Git仓库、获取仓库详细信息、提取项目元数据\n" +
                                "- **项目管理**: 项目注册、配置管理、状态跟踪\n" +
                                "- **任务管理**: 代码分析任务、文档生成任务的创建和监控\n" +
                                "- **文档管理**: 自动生成项目文档、版本管理、静态站点生成\n" +
                                "- **服务发现**: 服务注册、发现、能力管理和需求匹配\n\n" +
                                "## 服务发现系统\n" +
                                "服务发现系统提供了完整的服务注册和发现功能：\n" +
                                "- **服务注册**: 注册、更新和删除服务\n" +
                                "- **服务发现**: 多种条件查询和过滤服务\n" +
                                "- **能力管理**: 管理服务提供的能力和示例\n" +
                                "- **需求匹配**: 基于需求智能匹配合适的服务\n" +
                                "- **反馈系统**: 收集和处理用户反馈以改进推荐\n\n" +
                                "## 快速开始\n" +
                                "1. 使用Git仓库验证接口验证您的代码仓库\n" +
                                "2. 注册项目并配置分析选项\n" +
                                "3. 查看生成的文档和分析结果\n" +
                                "4. 使用服务发现功能管理和查找服务")
                        .version("1.0.0")
                        .contact(new Contact()
                                .name("ArchScope团队")
                                .email("<EMAIL>")
                                .url("https://www.archscope.com"))
                        .license(new License()
                                .name("MIT License")
                                .url("https://opensource.org/licenses/MIT")))

                .servers(Arrays.asList(
                        new Server().url("http://localhost:8080").description("开发环境"),
                        new Server().url("https://api.archscope.com").description("生产环境")
                ))

                .externalDocs(new ExternalDocumentation()
                        .description("ArchScope 完整文档")
                        .url("https://docs.archscope.com"))

                .tags(Arrays.asList(
                        new Tag().name("Git仓库管理")
                                .description("Git仓库验证、信息获取和元数据提取相关接口。支持GitHub、GitLab、Bitbucket等主流Git托管平台。"),
                        new Tag().name("项目管理")
                                .description("项目注册、配置管理、状态查询等核心功能接口。"),
                        new Tag().name("任务管理")
                                .description("代码分析任务、文档生成任务的创建、监控和管理接口。"),
                        new Tag().name("文档管理")
                                .description("项目文档的生成、版本管理和静态站点生成接口。"),
                        new Tag().name("系统管理")
                                .description("系统状态、配置管理和监控相关接口。"),
                        new Tag().name("服务注册管理")
                                .description("服务注册、更新、删除等管理操作。支持REST API、微服务等多种服务类型的注册和生命周期管理。" +
                                        "\n\n**主要功能**：\n" +
                                        "- 服务注册：注册新服务到注册中心\n" +
                                        "- 服务更新：更新已注册服务的信息\n" +
                                        "- 服务注销：从注册中心移除服务\n" +
                                        "- 服务查询：获取服务详细信息\n\n" +
                                        "**支持的服务类型**：REST_API, GRAPHQL_API, GRPC_SERVICE, MESSAGE_QUEUE, DATABASE, CACHE, OTHER"),
                        new Tag().name("服务发现")
                                .description("服务查询和发现的REST API。支持按名称、类型、标签、Maven坐标等多种条件查询服务，提供分页和排序功能。" +
                                        "\n\n**查询方式**：\n" +
                                        "- 按名称搜索（支持模糊匹配）\n" +
                                        "- 按服务类型过滤\n" +
                                        "- 按状态过滤\n" +
                                        "- 按标签查找\n" +
                                        "- 按Maven坐标查找\n" +
                                        "- 按能力名称查找\n" +
                                        "- 组合条件查询\n\n" +
                                        "**特性**：分页查询、排序、缓存优化"),
                        new Tag().name("能力管理")
                                .description("服务能力注册、查询和管理相关操作。管理服务提供的具体能力，包括能力描述、示例和版本信息。" +
                                        "\n\n**能力管理功能**：\n" +
                                        "- 能力注册：为服务注册新的能力\n" +
                                        "- 能力查询：搜索和查找能力\n" +
                                        "- 能力更新：更新能力信息\n" +
                                        "- 能力废弃：标记能力为废弃状态\n" +
                                        "- 示例管理：管理能力使用示例\n\n" +
                                        "**能力示例包含**：请求示例、响应示例、使用说明"),
                        new Tag().name("需求匹配")
                                .description("需求匹配和服务推荐相关操作。基于用户需求智能匹配合适的服务，提供推荐分数和匹配原因。" +
                                        "\n\n**匹配算法**：\n" +
                                        "- 基于能力匹配度计算\n" +
                                        "- 考虑服务质量和可用性\n" +
                                        "- 结合用户反馈优化推荐\n" +
                                        "- 支持多种匹配策略\n\n" +
                                        "**推荐结果包含**：匹配分数、匹配原因、置信度、相关能力列表\n\n" +
                                        "**反馈机制**：支持用户对推荐结果进行评价，持续改进匹配算法")
                ))

                .components(new Components()
                        .addSecuritySchemes("bearerAuth", new SecurityScheme()
                                .type(SecurityScheme.Type.HTTP)
                                .scheme("bearer")
                                .bearerFormat("JWT")
                                .description("JWT认证令牌"))
                        .addSecuritySchemes("apiKey", new SecurityScheme()
                                .type(SecurityScheme.Type.APIKEY)
                                .in(SecurityScheme.In.HEADER)
                                .name("X-API-Key")
                                .description("API密钥认证")));
    }
} 