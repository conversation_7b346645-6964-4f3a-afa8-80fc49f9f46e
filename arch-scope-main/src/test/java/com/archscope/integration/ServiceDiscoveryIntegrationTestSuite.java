package com.archscope.integration;

import org.junit.jupiter.api.DisplayName;
import org.junit.platform.suite.api.SelectClasses;
import org.junit.platform.suite.api.Suite;

/**
 * 服务发现系统集成测试套件
 * 包含所有服务发现相关的集成测试
 */
@Suite
@SelectClasses({
    ServiceDiscoveryIntegrationTest.class,
    ServiceDiscoveryApiIntegrationTest.class
})
@DisplayName("服务发现系统集成测试套件")
class ServiceDiscoveryIntegrationTestSuite {
    // 测试套件类，用于组织和运行所有相关的集成测试
}