version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: archscope-mysql
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: archscope
      MYSQL_USER: test
      MYSQL_PASSWORD: test
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init-db:/docker-entrypoint-initdb.d
    command: --default-authentication-plugin=mysql_native_password
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  redis:
    image: redis:7-alpine
    container_name: archscope-redis
    command: redis-server --requirepass test
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      timeout: 20s
      retries: 10

  rocketmq-namesrv:
    image: apache/rocketmq:4.9.4
    container_name: archscope-rocketmq-namesrv
    ports:
      - "9876:9876"
    volumes:
      - rocketmq_logs:/opt/logs
      - rocketmq_store:/opt/store
    environment:
      JAVA_OPT_EXT: "-Duser.home=/opt -Xms512M -Xmx512M -Xmn128m"
    command: ["sh", "mqnamesrv"]
    restart: unless-stopped

  rocketmq-broker:
    image: apache/rocketmq:4.9.4
    container_name: archscope-rocketmq-broker
    ports:
      - "10909:10909"
      - "10911:10911"
    volumes:
      - rocketmq_logs:/opt/logs
      - rocketmq_store:/opt/store
      - ./rocketmq/broker.conf:/opt/rocketmq-4.9.4/conf/broker.conf
    environment:
      NAMESRV_ADDR: "rocketmq-namesrv:9876"
      JAVA_OPT_EXT: "-Duser.home=/opt -Xms512M -Xmx512M -Xmn128m"
    command: ["sh", "mqbroker", "-c", "/opt/rocketmq-4.9.4/conf/broker.conf"]
    depends_on:
      - rocketmq-namesrv
    restart: unless-stopped

volumes:
  mysql_data:
  redis_data:
  rocketmq_logs:
  rocketmq_store:

networks:
  default:
    name: archscope-network
