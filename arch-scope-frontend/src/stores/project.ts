import { defineStore } from 'pinia';
import request from '@/utils/request';
import { ref } from 'vue';

export interface Project {
  id: number;
  name: string;
  description: string;
  repositoryUrl: string;
  branch: string;
  createdAt: string;
  updatedAt: string;
  lastAnalyzedAt: string | null;
  creatorId: number;
  status: string;
  active: boolean;
  documentationPath: string | null;
  analysisCount: number;
  documentationVersion: number;
  rating: number;  // 项目评分
  icon: string | null;  // 项目图标
  type: string;  // 项目类型

  // UI扩展字段
  iconUrl?: string;  // 项目图标URL
  repoUrl?: string;  // 仓库URL别名
  healthAssessment?: {
    score: number;
  };
  stats?: {
    fileCount: number;
    lineCount: number;
  };
  docGenerations?: Array<{
    id: number;
    version: string;
    generatedAt: string;
  }>;

  // 可能的后端字段
  fileCount?: number;  // 文件数
  lineCount?: number;  // 代码行数
  codeLines?: number;  // 代码行数别名
  linesOfCode?: number;  // 代码行数（后端字段）
  contributorCount?: number;  // 贡献者数量
  contributors?: number;  // 贡献者数量别名
}

export interface DocumentVersion {
  id: number;
  projectId: number;
  commitId: string;
  contentPath: string;
  timestamp: string;
  docType: string;
  versionTag: string;
  title: string;
  description: string;
  author: string;
  lastModified: string;
  isPublished: boolean;
  status: string;
}

export const useProjectStore = defineStore('project', () => {
  // 状态
  const projects = ref<Project[]>([]);
  const currentProject = ref<Project | null>(null);
  const loading = ref(false);
  const error = ref<string | null>(null);

  // 获取所有项目
  const fetchProjects = async () => {
    loading.value = true;
    error.value = null;
    try {
      console.log('🔍 开始获取项目列表...');
      const response = await request.get('/projects');
      console.log('📡 API响应:', response);

      // 处理不同的响应格式
      let projectsData: Project[] = [];
      const responseData = response as any;  // 临时使用any类型来处理不同的响应格式

      if (Array.isArray(responseData)) {
        // 直接是数组
        projectsData = responseData;
      } else if (responseData.data && Array.isArray(responseData.data)) {
        // 包装在data字段中
        projectsData = responseData.data;
      } else if (responseData.content && Array.isArray(responseData.content)) {
        // Spring Boot分页格式
        projectsData = responseData.content;
      } else {
        console.warn('⚠️ 未知的响应格式:', responseData);
        projectsData = [];
      }

      projects.value = projectsData;
      console.log('✅ 项目列表获取成功，项目数量:', projects.value.length);
      return projects.value;
    } catch (err: any) {
      console.error('❌ 获取项目列表失败:', err);
      error.value = err.message || '获取项目列表失败';

      // 如果API失败，尝试使用模拟数据
      const mockData = localStorage.getItem('mock_projects_data');
      if (mockData) {
        try {
          const parsed = JSON.parse(mockData);
          const mockProjects = parsed.data || parsed.content || parsed;
          if (Array.isArray(mockProjects)) {
            console.log('📦 使用模拟数据:', mockProjects);
            projects.value = mockProjects;
            error.value = null; // 清除错误状态
            return projects.value;
          }
        } catch (parseError) {
          console.error('解析模拟数据失败:', parseError);
        }
      }

      throw err;
    } finally {
      loading.value = false;
    }
  };

  // 获取项目详情
  const fetchProjectById = async (id: number) => {
    loading.value = true;
    error.value = null;
    try {
      console.log('🔍 ProjectStore: 开始获取项目详情，ID:', id);
      const response = await request.get(`/projects/${id}`);
      console.log('📡 ProjectStore: API响应:', response);

      // 处理不同的响应格式
      let projectData: Project;
      const responseData = response as any;

      if (responseData.data) {
        // 包装在data字段中
        projectData = responseData.data;
      } else if (responseData.id) {
        // 直接是项目对象
        projectData = responseData;
      } else {
        console.warn('⚠️ ProjectStore: 未知的响应格式:', responseData);
        throw new Error('无效的项目数据格式');
      }

      currentProject.value = projectData;
      console.log('✅ ProjectStore: 项目详情获取成功:', currentProject.value);
      return currentProject.value;
    } catch (err: any) {
      console.error('❌ ProjectStore: 获取项目详情失败:', err);
      error.value = err.message || '获取项目详情失败';
      throw err;
    } finally {
      loading.value = false;
    }
  };

  // 注册新项目
  const registerProject = async (
    name: string,
    description: string,
    repositoryUrl: string,
    branch: string = 'main'
  ) => {
    // 防重复调用检查
    if (loading.value) {
      console.warn('项目注册正在进行中，忽略重复调用')
      throw new Error('项目注册正在进行中，请勿重复提交')
    }

    loading.value = true;
    console.log('ProjectStore: 开始注册项目', { name, repositoryUrl, branch })

    try {
      const response = await request.post('/projects', {
        name,
        description,
        repositoryUrl,
        branch
      });

      console.log('ProjectStore: 项目注册API调用成功', response.data)

      // 如果注册成功，刷新项目列表
      await fetchProjects();

      return response.data;
    } catch (error: any) {
      // 提取后端返回的具体错误信息
      let errorMessage = '项目注册失败，请稍后再试';

      if (error.response && error.response.data) {
        // 尝试从响应体中提取错误信息
        const responseData = error.response.data;

        if (typeof responseData === 'string') {
          errorMessage = responseData;
        } else if (responseData.message) {
          errorMessage = responseData.message;
        } else if (responseData.error) {
          errorMessage = responseData.error;
        } else if (responseData.errorMessage) {
          errorMessage = responseData.errorMessage;
        }
      } else if (error.message) {
        // 如果没有响应体，使用错误消息
        errorMessage = error.message;
      }

      // 重新抛出带有友好错误信息的错误
      const friendlyError = new Error(errorMessage);
      friendlyError.name = 'ProjectRegistrationError';
      throw friendlyError;
    } finally {
      loading.value = false;
    }
  };

  // 触发项目代码分析
  const analyzeProject = async (projectId: number) => {
    loading.value = true;
    try {
      const response = await request.post(`/projects/${projectId}/analyze`);

      // 更新当前项目状态
      if (currentProject.value && currentProject.value.id === projectId) {
        currentProject.value = response.data;
      }

      // 更新项目列表中的项目
      const index = projects.value.findIndex(p => p.id === projectId);
      if (index !== -1) {
        projects.value[index] = response.data;
      }

      return response.data;
    } finally {
      loading.value = false;
    }
  };

  // 生成项目文档
  const generateDocumentation = async (projectId: number) => {
    loading.value = true;
    try {
      const response = await request.post(`/projects/${projectId}/generate-docs`);

      // 更新当前项目状态
      if (currentProject.value && currentProject.value.id === projectId) {
        currentProject.value = response.data;
      }

      // 更新项目列表中的项目
      const index = projects.value.findIndex(p => p.id === projectId);
      if (index !== -1) {
        projects.value[index] = response.data;
      }

      return response.data;
    } finally {
      loading.value = false;
    }
  };

  // 获取项目文档版本列表
  const fetchDocumentVersions = async (projectId: number) => {
    loading.value = true;
    try {
      const response = await request.get(`/document-versions/project/${projectId}`);
      return response.data;
    } finally {
      loading.value = false;
    }
  };

  // 获取文档内容
  const fetchDocumentContent = async (projectId: number, versionId: number, docType: string) => {
    loading.value = true;
    try {
      const response = await request.get(`/document-versions/${versionId}/content`, {
        params: { docType }
      });
      return response.data;
    } finally {
      loading.value = false;
    }
  };

  // 比较文档版本
  const compareDocumentVersions = async (fromVersionId: number, toVersionId: number) => {
    loading.value = true;
    try {
      const response = await request.get(`/document-versions/compare`, {
        params: { fromVersionId, toVersionId }
      });
      return response.data;
    } finally {
      loading.value = false;
    }
  };

  // 生成文档
  const generateDocs = async (projectId: number) => {
    loading.value = true;
    error.value = null;
    try {
      const response = await request.post(`/projects/${projectId}/generate-docs`);
      return response.data;
    } catch (err: any) {
      error.value = err.message || '生成文档失败';
      throw err;
    } finally {
      loading.value = false;
    }
  };

  // 删除项目
  const deleteProject = async (projectId: number) => {
    loading.value = true;
    error.value = null;
    try {
      await request.delete(`/projects/${projectId}`);
      projects.value = projects.value.filter(p => p.id !== projectId);
      if (currentProject.value?.id === projectId) {
        currentProject.value = null;
      }
    } catch (err: any) {
      error.value = err.message || '删除项目失败';
      throw err;
    } finally {
      loading.value = false;
    }
  };

  return {
    // 状态
    projects,
    currentProject,
    loading,
    error,

    // 方法
    fetchProjects,
    fetchProjectById,
    registerProject,
    analyzeProject,
    generateDocumentation,
    generateDocs,
    deleteProject,
    fetchDocumentVersions,
    fetchDocumentContent,
    compareDocumentVersions
  };
});