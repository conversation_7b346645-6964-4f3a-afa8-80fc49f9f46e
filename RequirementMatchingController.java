package com.archscope.app.controller;

import com.archscope.app.command.RecommendationFeedbackCommand;
import com.archscope.app.command.RequirementMatchingCommand;
import com.archscope.app.dto.FeedbackDTO;
import com.archscope.app.dto.RequirementDTO;
import com.archscope.app.dto.ServiceRecommendationDTO;
import com.archscope.app.service.RequirementMatchingService;
import com.archscope.facade.dto.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 需求匹配控制器
 * 专门处理需求匹配和服务推荐相关操作
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/requirements")
@RequiredArgsConstructor
@Validated
@Tag(name = "需求匹配", description = "需求匹配和服务推荐相关操作")
public class RequirementMatchingController {

    private final RequirementMatchingService requirementMatchingService;

    @Operation(summary = "根据需求匹配服务", description = "根据需求描述和能力要求匹配合适的服务")
    @PostMapping("/match")
    public ResponseEntity<ApiResponse<List<ServiceRecommendationDTO>>> matchServices(
            @Valid @RequestBody RequirementMatchingCommand command) {
        
        log.info("需求匹配请求: description={}", command.getDescription());
        
        try {
            List<ServiceRecommendationDTO> recommendations = 
                requirementMatchingService.findServicesForRequirement(command);
            log.info("需求匹配成功，找到{}个推荐服务", recommendations.size());
            return ResponseEntity.ok(ApiResponse.success("需求匹配成功", recommendations));
        } catch (Exception e) {
            log.error("需求匹配失败: description={}", command.getDescription(), e);
            throw e;
        }
    }

    @Operation(summary = "根据需求DTO匹配服务", description = "根据完整的需求信息匹配服务")
    @PostMapping("/match-by-dto")
    public ResponseEntity<ApiResponse<List<ServiceRecommendationDTO>>> matchServicesByDTO(
            @Valid @RequestBody RequirementDTO requirementDTO) {
        
        log.info("根据需求DTO匹配服务请求: id={}", requirementDTO.getId());
        
        try {
            List<ServiceRecommendationDTO> recommendations = 
                requirementMatchingService.findServicesForRequirement(requirementDTO);
            log.info("根据需求DTO匹配服务成功，找到{}个推荐服务", recommendations.size());
            return ResponseEntity.ok(ApiResponse.success("需求匹配成功", recommendations));
        } catch (Exception e) {
            log.error("根据需求DTO匹配服务失败: id={}", requirementDTO.getId(), e);
            throw e;
        }
    }

    @Operation(summary = "生成能力需求", description = "根据需求描述自动生成能力需求列表")
    @PostMapping("/generate-capabilities")
    public ResponseEntity<ApiResponse<List<String>>> generateCapabilityRequirements(
            @Parameter(description = "需求描述") @RequestParam @NotBlank(message = "需求描述不能为空") String description) {
        
        log.info("生成能力需求请求: description={}", description);
        
        try {
            List<String> capabilities = requirementMatchingService.generateCapabilityRequirements(description);
            log.info("生成能力需求成功，生成{}个能力", capabilities.size());
            return ResponseEntity.ok(ApiResponse.success("生成能力需求成功", capabilities));
        } catch (Exception e) {
            log.error("生成能力需求失败: description={}", description, e);
            throw e;
        }
    }

    @Operation(summary = "获取服务推荐", description = "根据服务ID列表获取推荐信息")
    @PostMapping("/recommendations")
    public ResponseEntity<ApiResponse<List<ServiceRecommendationDTO>>> getServiceRecommendations(
            @Parameter(description = "服务ID列表") @RequestBody @NotEmpty(message = "服务ID列表不能为空") List<String> serviceIds) {
        
        log.info("获取服务推荐请求: serviceIds={}", serviceIds);
        
        try {
            List<ServiceRecommendationDTO> recommendations = 
                requirementMatchingService.getServiceRecommendations(serviceIds);
            log.info("获取服务推荐成功，返回{}个推荐", recommendations.size());
            return ResponseEntity.ok(ApiResponse.success("获取服务推荐成功", recommendations));
        } catch (Exception e) {
            log.error("获取服务推荐失败: serviceIds={}", serviceIds, e);
            throw e;
        }
    }

    @Operation(summary = "记录推荐反馈", description = "记录用户对推荐结果的反馈")
    @PostMapping("/feedback")
    public ResponseEntity<ApiResponse<FeedbackDTO>> recordFeedback(
            @Valid @RequestBody RecommendationFeedbackCommand command) {
        
        log.info("记录推荐反馈请求: recommendationId={}", command.getRecommendationId());
        
        try {
            FeedbackDTO feedback = requirementMatchingService.recordRecommendationFeedback(command);
            log.info("记录推荐反馈成功: feedbackId={}", feedback.getId());
            return ResponseEntity.ok(ApiResponse.success("记录推荐反馈成功", feedback));
        } catch (Exception e) {
            log.error("记录推荐反馈失败: recommendationId={}", command.getRecommendationId(), e);
            throw e;
        }
    }

    @Operation(summary = "记录推荐反馈（按ID）", description = "根据推荐ID记录反馈信息")
    @PostMapping("/feedback/{recommendationId}")
    public ResponseEntity<ApiResponse<FeedbackDTO>> recordFeedbackById(
            @Parameter(description = "推荐ID") @PathVariable @NotBlank(message = "推荐ID不能为空") String recommendationId,
            @Valid @RequestBody FeedbackDTO feedback) {
        
        log.info("根据推荐ID记录反馈请求: recommendationId={}", recommendationId);
        
        try {
            FeedbackDTO result = requirementMatchingService.recordRecommendationFeedback(recommendationId, feedback);
            log.info("根据推荐ID记录反馈成功: feedbackId={}", result.getId());
            return ResponseEntity.ok(ApiResponse.success("记录推荐反馈成功", result));
        } catch (Exception e) {
            log.error("根据推荐ID记录反馈失败: recommendationId={}", recommendationId, e);
            throw e;
        }
    }
}