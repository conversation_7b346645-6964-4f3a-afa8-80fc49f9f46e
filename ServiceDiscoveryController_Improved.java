package com.archscope.app.controller;

import com.archscope.app.dto.ServiceDTO;
import com.archscope.app.service.ServiceDiscoveryService;
import com.archscope.facade.dto.ApiResponse;
import com.archscope.facade.dto.PageResponseDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 服务发现控制器
 * 专门处理服务发现和查询相关操作
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/discovery")
@RequiredArgsConstructor
@Validated
@Tag(name = "服务发现", description = "服务发现和查询相关操作")
public class ServiceDiscoveryController {

    private final ServiceDiscoveryService serviceDiscoveryService;

    @Operation(summary = "搜索服务", description = "根据关键词搜索服务")
    @GetMapping("/search")
    @Cacheable(value = "service-search", key = "#keyword + '_' + #page + '_' + #size")
    public ResponseEntity<ApiResponse<PageResponseDTO<ServiceDTO>>> searchServices(
            @Parameter(description = "搜索关键词") @RequestParam @NotBlank(message = "搜索关键词不能为空") String keyword,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") @Min(1) Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") @Min(1) Integer size) {
        
        log.info("搜索服务请求: keyword={}, page={}, size={}", keyword, page, size);
        
        try {
            PageResponseDTO<ServiceDTO> result = serviceDiscoveryService.searchServices(keyword, page, size);
            log.info("搜索服务成功，找到{}个结果", result.getTotal());
            return ResponseEntity.ok(ApiResponse.success("搜索服务成功", result));
        } catch (Exception e) {
            log.error("搜索服务失败: keyword={}", keyword, e);
            throw e;
        }
    }

    @Operation(summary = "按标签查找服务", description = "根据标签查找相关服务")
    @GetMapping("/by-tags")
    @Cacheable(value = "service-by-tags", key = "#tags.hashCode() + '_' + #page + '_' + #size")
    public ResponseEntity<ApiResponse<PageResponseDTO<ServiceDTO>>> findServicesByTags(
            @Parameter(description = "标签列表") @RequestParam List<String> tags,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") @Min(1) Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") @Min(1) Integer size) {
        
        log.info("按标签查找服务请求: tags={}, page={}, size={}", tags, page, size);
        
        try {
            PageResponseDTO<ServiceDTO> result = serviceDiscoveryService.findServicesByTags(tags, page, size);
            log.info("按标签查找服务成功，找到{}个结果", result.getTotal());
            return ResponseEntity.ok(ApiResponse.success("按标签查找服务成功", result));
        } catch (Exception e) {
            log.error("按标签查找服务失败: tags={}", tags, e);
            throw e;
        }
    }

    @Operation(summary = "按Maven坐标查找服务", description = "根据Maven坐标查找服务")
    @GetMapping("/by-maven")
    @Cacheable(value = "service-by-maven", key = "#groupId + '_' + #artifactId")
    public ResponseEntity<ApiResponse<List<ServiceDTO>>> findServicesByMavenCoordinate(
            @Parameter(description = "Group ID") @RequestParam @NotBlank(message = "Group ID不能为空") String groupId,
            @Parameter(description = "Artifact ID") @RequestParam @NotBlank(message = "Artifact ID不能为空") String artifactId) {
        
        log.info("按Maven坐标查找服务请求: groupId={}, artifactId={}", groupId, artifactId);
        
        try {
            List<ServiceDTO> result = serviceDiscoveryService.findServicesByMavenCoordinate(groupId, artifactId);
            log.info("按Maven坐标查找服务成功，找到{}个结果", result.size());
            return ResponseEntity.ok(ApiResponse.success("按Maven坐标查找服务成功", result));
        } catch (Exception e) {
            log.error("按Maven坐标查找服务失败: groupId={}, artifactId={}", groupId, artifactId, e);
            throw e;
        }
    }

    @Operation(summary = "获取热门服务", description = "获取使用频率最高的服务列表")
    @GetMapping("/popular")
    @Cacheable(value = "popular-services", key = "#limit")
    public ResponseEntity<ApiResponse<List<ServiceDTO>>> getPopularServices(
            @Parameter(description = "返回数量限制") @RequestParam(defaultValue = "10") @Min(1) Integer limit) {
        
        log.info("获取热门服务请求: limit={}", limit);
        
        try {
            List<ServiceDTO> result = serviceDiscoveryService.getPopularServices(limit);
            log.info("获取热门服务成功，返回{}个结果", result.size());
            return ResponseEntity.ok(ApiResponse.success("获取热门服务成功", result));
        } catch (Exception e) {
            log.error("获取热门服务失败: limit={}", limit, e);
            throw e;
        }
    }
}