package com.archscope.facade.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 任务结果数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskResultDTO {
    
    /**
     * 任务ID
     */
    private Long taskId;
    
    /**
     * 项目ID
     */
    private Long projectId;
    
    /**
     * 任务状态
     */
    private String status;
    
    /**
     * 任务整体状态（COMPLETED/FAILED/PARTIAL_SUCCESS）
     */
    private String overallStatus;
    
    /**
     * 任务结果
     */
    private String result;
    
    /**
     * 任务结果JSON字符串（支持多文档类型）
     */
    private String results;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 错误详情
     */
    private String errorDetails;
    
    /**
     * 执行时间（毫秒）
     */
    private Long executionTime;
    
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 处理的文件数量
     */
    private Integer processedFileCount;
    
    /**
     * 生成的文档类型
     */
    private String documentType;
    
    /**
     * 额外的元数据
     */
    private Map<String, Object> metadata;
    
    /**
     * 关联的Git提交ID
     */
    private String commitId;
}
