package com.archscope.facade.dto;

import lombok.Getter;

/**
 * 结果码枚举
 */
@Getter
public enum ResultCode {
    
    // 成功
    SUCCESS(200, "操作成功"),
    
    // 客户端错误
    BAD_REQUEST(400, "请求参数错误"),
    UNAUTHORIZED(401, "未授权"),
    FORBIDDEN(403, "禁止访问"),
    NOT_FOUND(404, "资源不存在"),
    METHOD_NOT_ALLOWED(405, "请求方法不允许"),
    
    // 服务器错误
    INTERNAL_SERVER_ERROR(500, "服务器内部错误"),
    SERVICE_UNAVAILABLE(503, "服务不可用"),
    
    // 业务错误
    PARAMETER_VALIDATION_ERROR(1000, "参数验证失败"),
    USER_NOT_EXIST(1001, "用户不存在"),
    USER_ALREADY_EXIST(1002, "用户已存在"),
    PASSWORD_ERROR(1003, "密码错误"),
    TOKEN_INVALID(1004, "Token无效"),
    PROJECT_NOT_EXIST(1005, "项目不存在"),
    GIT_REPOSITORY_ERROR(1006, "Git仓库错误"),
    GIT_REPOSITORY_NOT_EXIST(1007, "Git仓库不存在"),
    REPOSITORY_CLONE_ERROR(1008, "仓库克隆失败"),
    DOCUMENT_GENERATION_ERROR(1009, "文档生成失败"),
    
    // 服务发现相关错误 (2000-2099)
    SERVICE_NOT_FOUND(2000, "服务不存在"),
    SERVICE_ALREADY_EXISTS(2001, "服务已存在"),
    INVALID_SERVICE_DATA(2002, "无效的服务数据"),
    SERVICE_REGISTRATION_FAILED(2003, "服务注册失败"),
    SERVICE_UPDATE_FAILED(2004, "服务更新失败"),
    CAPABILITY_NOT_FOUND(2005, "能力不存在"),
    INVALID_CAPABILITY_DATA(2006, "无效的能力数据"),
    CAPABILITY_REGISTRATION_FAILED(2007, "能力注册失败"),
    REQUIREMENT_NOT_FOUND(2008, "需求不存在"),
    INVALID_REQUIREMENT_DATA(2009, "无效的需求数据"),
    REQUIREMENT_MATCHING_FAILED(2010, "需求匹配失败"),
    DATABASE_ACCESS_ERROR(2011, "数据库访问错误"),
    EXTERNAL_SERVICE_ERROR(2012, "外部服务通信错误"),
    DATA_MAPPING_ERROR(2013, "数据映射错误");
    
    /**
     * 状态码
     */
    private final Integer code;
    
    /**
     * 消息
     */
    private final String message;
    
    ResultCode(Integer code, String message) {
        this.code = code;
        this.message = message;
    }
} 