package com.archscope.facade.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 任务数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskDTO {
    
    /**
     * 任务ID
     */
    private Long id;
    
    /**
     * 项目ID
     */
    private Long projectId;
    
    /**
     * 任务名称
     */
    private String name;
    
    /**
     * 任务描述
     */
    private String description;
    
    /**
     * 任务类型
     */
    private String type;
    
    /**
     * 任务状态
     */
    private String status;
    
    /**
     * 详细状态描述
     */
    private String detailedStatus;
    
    /**
     * 任务优先级
     */
    private Integer priority;
    
    /**
     * 进度百分比（0-100）
     */
    private Integer progress;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 开始处理时间
     */
    private LocalDateTime processingStartedAt;
    
    /**
     * 超时时间
     */
    private LocalDateTime timeoutAt;
    
    /**
     * 执行时间（毫秒）
     */
    private Long executionTime;
    
    /**
     * 重试次数
     */
    private Integer retryCount;
    
    /**
     * 最大重试次数
     */
    private Integer maxRetries;
    
    /**
     * 错误日志
     */
    private String errorLog;
    
    /**
     * 最后一次错误详情
     */
    private String lastErrorDetail;
    
    /**
     * 任务结果
     */
    private String result;
    
    /**
     * 任务结果JSON字符串（支持多文档类型）
     */
    private String results;
    
    /**
     * 任务参数
     */
    private Map<String, Object> parameters;
    
    /**
     * 创建用户ID
     */
    private Long userId;
    
    /**
     * 分配用户ID
     */
    private Long assigneeId;
    
    /**
     * LLM工作节点ID
     */
    private String workerId;
    
    /**
     * 任务整体状态（COMPLETED/FAILED/PARTIAL_SUCCESS）
     */
    private String overallStatus;
    
    /**
     * 关联的Git提交ID
     */
    private String commitId;
    
    /**
     * 任务版本号
     */
    private String taskVersion;
}
