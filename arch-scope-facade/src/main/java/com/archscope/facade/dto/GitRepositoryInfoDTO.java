package com.archscope.facade.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Git仓库信息数据传输对象
 * 用于返回从Git仓库URL解析出的项目元数据信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GitRepositoryInfoDTO {

    /**
     * 项目名称（从仓库URL中解析）
     */
    private String projectName;

    /**
     * 项目描述（从README.md或仓库描述中获取）
     */
    private String description;

    /**
     * 默认分支名称
     */
    private String defaultBranch;

    /**
     * 所有可用分支列表
     */
    private List<String> branches;

    /**
     * 仓库类型（GitHub, GitLab等）
     */
    private String repositoryType;

    /**
     * 仓库所有者/组织名称
     */
    private String owner;

    /**
     * 仓库名称
     */
    private String repositoryName;

    /**
     * 是否为私有仓库
     */
    private Boolean isPrivate;

    /**
     * 仓库语言信息
     */
    private List<String> languages;
    
    /**
     * 仓库创建时间
     */
    private String createdAt;

    /**
     * 最后更新时间
     */
    private String updatedAt;

    /**
     * 仓库大小（KB）
     */
    private Long size;

    /**
     * Star数量
     */
    private Integer starCount;

    /**
     * Fork数量
     */
    private Integer forkCount;

    /**
     * 是否成功获取信息
     */
    private Boolean success;

    /**
     * 错误信息（如果获取失败）
     */
    private String errorMessage;
}
