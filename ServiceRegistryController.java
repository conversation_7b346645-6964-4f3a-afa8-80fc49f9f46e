package com.archscope.app.controller;

import com.archscope.app.command.ServiceRegistrationCommand;
import com.archscope.app.command.ServiceUpdateCommand;
import com.archscope.app.dto.ServiceDTO;
import com.archscope.app.service.ServiceRegistryService;
import com.archscope.facade.dto.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

/**
 * 服务注册管理控制器
 * 专门处理服务的注册、更新、注销等管理操作
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/services")
@RequiredArgsConstructor
@Validated
@Tag(name = "服务注册管理", description = "服务注册、更新、注销等管理操作")
public class ServiceRegistryController {

    private final ServiceRegistryService serviceRegistryService;

    @Operation(summary = "注册服务", description = "注册一个新的服务到服务注册中心")
    @PostMapping
    public ResponseEntity<ApiResponse<ServiceDTO>> registerService(
            @Valid @RequestBody ServiceRegistrationCommand command) {
        log.info("注册服务请求: {}", command.getName());
        
        try {
            ServiceDTO serviceDTO = serviceRegistryService.registerService(command);
            log.info("服务注册成功，服务ID: {}", serviceDTO.getId());
            return ResponseEntity.status(HttpStatus.CREATED)
                    .body(ApiResponse.success("服务注册成功", serviceDTO));
        } catch (Exception e) {
            log.error("服务注册失败: {}", command.getName(), e);
            throw e;
        }
    }

    @Operation(summary = "更新服务信息", description = "更新已注册服务的信息")
    @PutMapping("/{serviceId}")
    public ResponseEntity<ApiResponse<ServiceDTO>> updateService(
            @Parameter(description = "服务ID") @PathVariable @NotBlank(message = "服务ID不能为空") String serviceId,
            @Valid @RequestBody ServiceUpdateCommand command) {
        log.info("更新服务请求，服务ID: {}", serviceId);
        
        try {
            ServiceDTO serviceDTO = serviceRegistryService.updateService(serviceId, command);
            log.info("服务更新成功，服务ID: {}", serviceId);
            return ResponseEntity.ok(ApiResponse.success("服务更新成功", serviceDTO));
        } catch (Exception e) {
            log.error("服务更新失败，服务ID: {}", serviceId, e);
            throw e;
        }
    }

    @Operation(summary = "注销服务", description = "从服务注册中心注销指定服务")
    @DeleteMapping("/{serviceId}")
    public ResponseEntity<ApiResponse<Void>> deregisterService(
            @Parameter(description = "服务ID") @PathVariable @NotBlank(message = "服务ID不能为空") String serviceId) {
        log.info("注销服务请求，服务ID: {}", serviceId);
        
        try {
            serviceRegistryService.deregisterService(serviceId);
            log.info("服务注销成功，服务ID: {}", serviceId);
            return ResponseEntity.ok(ApiResponse.success("服务注销成功"));
        } catch (Exception e) {
            log.error("服务注销失败，服务ID: {}", serviceId, e);
            throw e;
        }
    }

    @Operation(summary = "获取服务详情", description = "根据服务ID获取服务的详细信息")
    @GetMapping("/{serviceId}")
    public ResponseEntity<ApiResponse<ServiceDTO>> getServiceById(
            @Parameter(description = "服务ID") @PathVariable @NotBlank(message = "服务ID不能为空") String serviceId) {
        log.info("获取服务详情请求，服务ID: {}", serviceId);
        
        try {
            ServiceDTO serviceDTO = serviceRegistryService.getServiceById(serviceId);
            return ResponseEntity.ok(ApiResponse.success("获取服务详情成功", serviceDTO));
        } catch (Exception e) {
            log.error("获取服务详情失败，服务ID: {}", serviceId, e);
            throw e;
        }
    }
}