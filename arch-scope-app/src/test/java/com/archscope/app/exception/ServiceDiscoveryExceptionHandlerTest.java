package com.archscope.app.exception;

import com.archscope.domain.exception.*;
import com.archscope.facade.dto.ApiResponse;
import com.archscope.facade.dto.ErrorDetail;
import com.archscope.facade.dto.ResultCode;
import com.archscope.infrastructure.exception.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.mock.web.MockHttpServletRequest;

import javax.servlet.http.HttpServletRequest;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

/**
 * 服务发现异常处理器测试
 */
@ExtendWith(MockitoExtension.class)
class ServiceDiscoveryExceptionHandlerTest {

    @InjectMocks
    private ServiceDiscoveryExceptionHandler exceptionHandler;

    private HttpServletRequest request;

    @BeforeEach
    void setUp() {
        request = new MockHttpServletRequest("POST", "/api/v1/services");
    }

    @Test
    void handleServiceNotFoundException_ShouldReturnNotFoundResponse() {
        // Given
        String serviceId = "test-service-id";
        ServiceNotFoundException exception = new ServiceNotFoundException(serviceId);

        // When
        ApiResponse<ErrorDetail> response = exceptionHandler.handleServiceNotFoundException(exception, request);

        // Then
        assertFalse(response.isSuccess());
        assertEquals(ResultCode.SERVICE_NOT_FOUND.getMessage(), response.getMessage());
        
        ErrorDetail errorDetail = response.getData();
        assertNotNull(errorDetail);
        assertEquals("SERVICE_NOT_FOUND", errorDetail.getErrorCode());
        assertEquals("/api/v1/services", errorDetail.getPath());
        assertNotNull(errorDetail.getTimestamp());
        assertTrue(errorDetail.getMessage().contains(serviceId));
    }

    @Test
    void handleDuplicateServiceException_ShouldReturnConflictResponse() {
        // Given
        String serviceName = "duplicate-service";
        DuplicateServiceException exception = new DuplicateServiceException(serviceName);

        // When
        ApiResponse<ErrorDetail> response = exceptionHandler.handleDuplicateServiceException(exception, request);

        // Then
        assertFalse(response.isSuccess());
        assertEquals(ResultCode.SERVICE_ALREADY_EXISTS.getMessage(), response.getMessage());
        
        ErrorDetail errorDetail = response.getData();
        assertNotNull(errorDetail);
        assertEquals("SERVICE_ALREADY_EXISTS", errorDetail.getErrorCode());
        assertEquals("/api/v1/services", errorDetail.getPath());
        assertNotNull(errorDetail.getContext());
        assertTrue(errorDetail.getContext().containsKey("suggestion"));
    }

    @Test
    void handleInvalidServiceDataException_ShouldReturnBadRequestResponse() {
        // Given
        String fieldName = "endpoint";
        String invalidValue = "invalid-url";
        InvalidServiceDataException exception = new InvalidServiceDataException(fieldName, invalidValue);

        // When
        ApiResponse<ErrorDetail> response = exceptionHandler.handleInvalidServiceDataException(exception, request);

        // Then
        assertFalse(response.isSuccess());
        assertEquals(ResultCode.INVALID_SERVICE_DATA.getMessage(), response.getMessage());
        
        ErrorDetail errorDetail = response.getData();
        assertNotNull(errorDetail);
        assertEquals("INVALID_SERVICE_DATA", errorDetail.getErrorCode());
        assertNotNull(errorDetail.getContext());
        assertTrue(errorDetail.getContext().containsKey("requiredFields"));
        assertTrue(errorDetail.getContext().containsKey("optionalFields"));
    }

    @Test
    void handleCapabilityNotFoundException_ShouldReturnNotFoundResponse() {
        // Given
        String capabilityId = "test-capability-id";
        CapabilityNotFoundException exception = new CapabilityNotFoundException(capabilityId);

        // When
        ApiResponse<ErrorDetail> response = exceptionHandler.handleCapabilityNotFoundException(exception, request);

        // Then
        assertFalse(response.isSuccess());
        assertEquals(ResultCode.CAPABILITY_NOT_FOUND.getMessage(), response.getMessage());
        
        ErrorDetail errorDetail = response.getData();
        assertNotNull(errorDetail);
        assertEquals("CAPABILITY_NOT_FOUND", errorDetail.getErrorCode());
        assertTrue(errorDetail.getMessage().contains(capabilityId));
    }

    @Test
    void handleDatabaseAccessException_ShouldReturnInternalServerErrorResponse() {
        // Given
        String operation = "findServiceById";
        DatabaseAccessException exception = new DatabaseAccessException(operation);

        // When
        ApiResponse<ErrorDetail> response = exceptionHandler.handleDatabaseAccessException(exception, request);

        // Then
        assertFalse(response.isSuccess());
        assertEquals(ResultCode.DATABASE_ACCESS_ERROR.getMessage(), response.getMessage());
        
        ErrorDetail errorDetail = response.getData();
        assertNotNull(errorDetail);
        assertEquals("DATABASE_ACCESS_ERROR", errorDetail.getErrorCode());
        assertEquals("数据库访问失败", errorDetail.getMessage());
        assertNotNull(errorDetail.getContext());
        assertTrue(errorDetail.getContext().containsKey("retryAfter"));
        assertTrue(errorDetail.getContext().containsKey("supportContact"));
    }

    @Test
    void handleExternalServiceCommunicationException_ShouldReturnServiceUnavailableResponse() {
        // Given
        String serviceName = "external-llm-service";
        ExternalServiceCommunicationException exception = new ExternalServiceCommunicationException(serviceName);

        // When
        ApiResponse<ErrorDetail> response = exceptionHandler.handleExternalServiceCommunicationException(exception, request);

        // Then
        assertFalse(response.isSuccess());
        assertEquals(ResultCode.EXTERNAL_SERVICE_ERROR.getMessage(), response.getMessage());
        
        ErrorDetail errorDetail = response.getData();
        assertNotNull(errorDetail);
        assertEquals("EXTERNAL_SERVICE_ERROR", errorDetail.getErrorCode());
        assertEquals("外部服务暂时不可用", errorDetail.getMessage());
        assertNotNull(errorDetail.getContext());
        assertTrue(errorDetail.getContext().containsKey("retryAfter"));
        assertTrue(errorDetail.getContext().containsKey("affectedOperations"));
    }

    @Test
    void handleServiceRegistrationFailedException_ShouldReturnInternalServerErrorResponse() {
        // Given
        String message = "Failed to register service due to validation error";
        ServiceRegistrationFailedException exception = new ServiceRegistrationFailedException(message);

        // When
        ApiResponse<ErrorDetail> response = exceptionHandler.handleServiceRegistrationFailedException(exception, request);

        // Then
        assertFalse(response.isSuccess());
        assertEquals(ResultCode.SERVICE_REGISTRATION_FAILED.getMessage(), response.getMessage());
        
        ErrorDetail errorDetail = response.getData();
        assertNotNull(errorDetail);
        assertEquals("SERVICE_REGISTRATION_FAILED", errorDetail.getErrorCode());
        assertTrue(errorDetail.getMessage().contains(message));
        assertNotNull(errorDetail.getContext());
        assertTrue(errorDetail.getContext().containsKey("possibleCauses"));
        assertTrue(errorDetail.getContext().containsKey("nextSteps"));
    }

    @Test
    void handleRequirementMatchingFailedException_ShouldReturnInternalServerErrorResponse() {
        // Given
        String message = "Matching algorithm failed to process complex requirement";
        RequirementMatchingFailedException exception = new RequirementMatchingFailedException(message);

        // When
        ApiResponse<ErrorDetail> response = exceptionHandler.handleRequirementMatchingFailedException(exception, request);

        // Then
        assertFalse(response.isSuccess());
        assertEquals(ResultCode.REQUIREMENT_MATCHING_FAILED.getMessage(), response.getMessage());
        
        ErrorDetail errorDetail = response.getData();
        assertNotNull(errorDetail);
        assertEquals("REQUIREMENT_MATCHING_FAILED", errorDetail.getErrorCode());
        assertTrue(errorDetail.getMessage().contains(message));
        assertNotNull(errorDetail.getContext());
        assertTrue(errorDetail.getContext().containsKey("suggestions"));
    }
}