package com.archscope.app.exception;

import com.archscope.facade.dto.ErrorDetail;
import org.junit.jupiter.api.Test;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.validation.BeanPropertyBindingResult;
import org.springframework.validation.FieldError;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.Path;
import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * 异常处理工具类测试
 */
class ExceptionHandlerUtilsTest {

    @Test
    void createFieldErrors_FromBindingResult_ShouldCreateCorrectFieldErrors() {
        // Given
        BeanPropertyBindingResult bindingResult = new BeanPropertyBindingResult(new Object(), "testObject");
        bindingResult.addError(new FieldError("testObject", "name", "invalid-name", false, null, null, "Name is required"));
        bindingResult.addError(new FieldError("testObject", "email", "invalid-email", false, null, null, "Email format is invalid"));

        // When
        List<ErrorDetail.FieldError> fieldErrors = ExceptionHandlerUtils.createFieldErrors(bindingResult);

        // Then
        assertEquals(2, fieldErrors.size());
        
        ErrorDetail.FieldError nameError = fieldErrors.stream()
                .filter(error -> "name".equals(error.getField()))
                .findFirst()
                .orElse(null);
        assertNotNull(nameError);
        assertEquals("Name is required", nameError.getMessage());
        assertEquals("invalid-name", nameError.getRejectedValue());
        
        ErrorDetail.FieldError emailError = fieldErrors.stream()
                .filter(error -> "email".equals(error.getField()))
                .findFirst()
                .orElse(null);
        assertNotNull(emailError);
        assertEquals("Email format is invalid", emailError.getMessage());
        assertEquals("invalid-email", emailError.getRejectedValue());
    }

    @Test
    void createFieldErrors_FromConstraintViolations_ShouldCreateCorrectFieldErrors() {
        // Given
        ConstraintViolation<?> violation1 = mock(ConstraintViolation.class);
        Path path1 = mock(Path.class);
        when(path1.toString()).thenReturn("name");
        when(violation1.getPropertyPath()).thenReturn(path1);
        when(violation1.getMessage()).thenReturn("Name cannot be null");
        when(violation1.getInvalidValue()).thenReturn(null);

        ConstraintViolation<?> violation2 = mock(ConstraintViolation.class);
        Path path2 = mock(Path.class);
        when(path2.toString()).thenReturn("age");
        when(violation2.getPropertyPath()).thenReturn(path2);
        when(violation2.getMessage()).thenReturn("Age must be positive");
        when(violation2.getInvalidValue()).thenReturn(-1);

        Set<ConstraintViolation<?>> violations = Set.of(violation1, violation2);

        // When
        List<ErrorDetail.FieldError> fieldErrors = ExceptionHandlerUtils.createFieldErrors(violations);

        // Then
        assertEquals(2, fieldErrors.size());
        
        ErrorDetail.FieldError nameError = fieldErrors.stream()
                .filter(error -> "name".equals(error.getField()))
                .findFirst()
                .orElse(null);
        assertNotNull(nameError);
        assertEquals("Name cannot be null", nameError.getMessage());
        assertNull(nameError.getRejectedValue());
        
        ErrorDetail.FieldError ageError = fieldErrors.stream()
                .filter(error -> "age".equals(error.getField()))
                .findFirst()
                .orElse(null);
        assertNotNull(ageError);
        assertEquals("Age must be positive", ageError.getMessage());
        assertEquals(-1, ageError.getRejectedValue());
    }

    @Test
    void createValidationErrorDetail_ShouldCreateCorrectErrorDetail() {
        // Given
        String errorCode = "VALIDATION_ERROR";
        String message = "Validation failed";
        String path = "/api/v1/services";
        List<ErrorDetail.FieldError> fieldErrors = List.of(
                new ErrorDetail.FieldError("name", "Name is required", null)
        );

        // When
        ErrorDetail errorDetail = ExceptionHandlerUtils.createValidationErrorDetail(
                errorCode, message, path, fieldErrors);

        // Then
        assertEquals(errorCode, errorDetail.getErrorCode());
        assertEquals(message, errorDetail.getMessage());
        assertEquals(path, errorDetail.getPath());
        assertEquals(fieldErrors, errorDetail.getFieldErrors());
        assertEquals("请检查以下字段的输入值", errorDetail.getDetails());
        assertNotNull(errorDetail.getTimestamp());
    }

    @Test
    void getRequestPath_WithValidRequest_ShouldReturnCorrectPath() {
        // Given
        HttpServletRequest request = new MockHttpServletRequest("POST", "/api/v1/services/register");

        // When
        String path = ExceptionHandlerUtils.getRequestPath(request);

        // Then
        assertEquals("/api/v1/services/register", path);
    }

    @Test
    void getRequestPath_WithNullRequest_ShouldReturnUnknown() {
        // When
        String path = ExceptionHandlerUtils.getRequestPath(null);

        // Then
        assertEquals("unknown", path);
    }

    @Test
    void createSimpleErrorDetail_ShouldCreateCorrectErrorDetail() {
        // Given
        String errorCode = "SERVICE_NOT_FOUND";
        String message = "Service not found";
        HttpServletRequest request = new MockHttpServletRequest("GET", "/api/v1/services/123");

        // When
        ErrorDetail errorDetail = ExceptionHandlerUtils.createSimpleErrorDetail(errorCode, message, request);

        // Then
        assertEquals(errorCode, errorDetail.getErrorCode());
        assertEquals(message, errorDetail.getMessage());
        assertEquals("/api/v1/services/123", errorDetail.getPath());
        assertNotNull(errorDetail.getTimestamp());
    }

    @Test
    void createDetailedErrorDetail_ShouldCreateCorrectErrorDetail() {
        // Given
        String errorCode = "DATABASE_ERROR";
        String message = "Database access failed";
        String details = "Connection timeout occurred";
        HttpServletRequest request = new MockHttpServletRequest("POST", "/api/v1/services");

        // When
        ErrorDetail errorDetail = ExceptionHandlerUtils.createDetailedErrorDetail(
                errorCode, message, details, request);

        // Then
        assertEquals(errorCode, errorDetail.getErrorCode());
        assertEquals(message, errorDetail.getMessage());
        assertEquals(details, errorDetail.getDetails());
        assertEquals("/api/v1/services", errorDetail.getPath());
        assertNotNull(errorDetail.getTimestamp());
    }

    @Test
    void extractKeyInfo_WithValidMessage_ShouldReturnFirstLine() {
        // Given
        String exceptionMessage = "Service not found: test-service\nCaused by: Database connection failed\n\tat com.example...";

        // When
        String keyInfo = ExceptionHandlerUtils.extractKeyInfo(exceptionMessage);

        // Then
        assertEquals("Service not found: test-service", keyInfo);
    }

    @Test
    void extractKeyInfo_WithNullMessage_ShouldReturnDefaultMessage() {
        // When
        String keyInfo = ExceptionHandlerUtils.extractKeyInfo(null);

        // Then
        assertEquals("未知错误", keyInfo);
    }

    @Test
    void extractKeyInfo_WithEmptyMessage_ShouldReturnDefaultMessage() {
        // When
        String keyInfo = ExceptionHandlerUtils.extractKeyInfo("");

        // Then
        assertEquals("未知错误", keyInfo);
    }

    @Test
    void isClientError_WithIllegalArgumentException_ShouldReturnTrue() {
        // Given
        Exception exception = new IllegalArgumentException("Invalid argument");

        // When
        boolean isClientError = ExceptionHandlerUtils.isClientError(exception);

        // Then
        assertTrue(isClientError);
    }

    @Test
    void isClientError_WithValidationException_ShouldReturnTrue() {
        // Given
        Exception exception = new RuntimeException("ValidationException occurred");

        // When
        boolean isClientError = ExceptionHandlerUtils.isClientError(exception);

        // Then
        assertTrue(isClientError);
    }

    @Test
    void isClientError_WithRuntimeException_ShouldReturnFalse() {
        // Given
        Exception exception = new RuntimeException("Database connection failed");

        // When
        boolean isClientError = ExceptionHandlerUtils.isClientError(exception);

        // Then
        assertFalse(isClientError);
    }

    @Test
    void isServerError_ShouldReturnOppositeOfIsClientError() {
        // Given
        Exception clientException = new IllegalArgumentException("Invalid argument");
        Exception serverException = new RuntimeException("Database connection failed");

        // When & Then
        assertTrue(ExceptionHandlerUtils.isClientError(clientException));
        assertFalse(ExceptionHandlerUtils.isServerError(clientException));
        
        assertFalse(ExceptionHandlerUtils.isClientError(serverException));
        assertTrue(ExceptionHandlerUtils.isServerError(serverException));
    }
}