package com.archscope.app.service;

import com.archscope.app.assembler.ServiceAssembler;
import com.archscope.app.command.ServiceRegistrationCommand;
import com.archscope.app.command.ServiceUpdateCommand;
import com.archscope.app.dto.ServiceDTO;
import com.archscope.app.exception.ServiceRegistrationFailedException;
import com.archscope.app.exception.ServiceUpdateFailedException;
import com.archscope.domain.model.servicediscovery.Service;
import com.archscope.domain.model.servicediscovery.ServiceStatus;
import com.archscope.domain.model.servicediscovery.ServiceType;
import com.archscope.domain.repository.ServiceRepository;
import com.archscope.domain.service.ServiceRegistrationDomainService;
import com.archscope.domain.valueobject.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 服务注册应用服务测试
 */
class ServiceRegistryServiceTest {

    @Mock
    private ServiceRepository serviceRepository;

    @Mock
    private ServiceRegistrationDomainService serviceRegistrationDomainService;

    @Mock
    private ServiceAssembler serviceAssembler;

    private ServiceRegistryService serviceRegistryService;

    private Service testService;
    private ServiceDTO testServiceDTO;
    private ServiceRegistrationCommand registrationCommand;
    private ServiceUpdateCommand updateCommand;

    @BeforeEach
    void setUp() throws MalformedURLException {
        MockitoAnnotations.openMocks(this);
        
        // 创建测试数据
        testService = Service.create(
                "测试服务",
                "这是一个测试服务",
                ServiceType.REST_API,
                Version.of("1.0.0"),
                new URL("https://api.example.com/v1"),
                "com.example",
                "test-service",
                Collections.singleton(Tag.of("api")),
                ServiceStatus.ACTIVE,
                Metadata.empty()
        );

        testServiceDTO = new ServiceDTO();
        testServiceDTO.setId(testService.getId().getValue());
        testServiceDTO.setName(testService.getName());
        testServiceDTO.setDescription(testService.getDescription());
        testServiceDTO.setType(testService.getType().name());
        testServiceDTO.setVersion(testService.getVersion().getValue());
        testServiceDTO.setEndpoint(testService.getEndpoint().toString());
        testServiceDTO.setGroupId(testService.getGroupId());
        testServiceDTO.setArtifactId(testService.getArtifactId());
        testServiceDTO.setStatus(testService.getStatus().name());
        testServiceDTO.setTags(Arrays.asList("api"));
        testServiceDTO.setMetadata(new HashMap<>());
        testServiceDTO.setCapabilities(new ArrayList<>());

        registrationCommand = new ServiceRegistrationCommand();
        registrationCommand.setName("测试服务");
        registrationCommand.setDescription("这是一个测试服务");
        registrationCommand.setType("REST_API");
        registrationCommand.setVersion("1.0.0");
        registrationCommand.setEndpoint("https://api.example.com/v1");
        registrationCommand.setGroupId("com.example");
        registrationCommand.setArtifactId("test-service");
        registrationCommand.setTags(Arrays.asList("api"));
        registrationCommand.setStatus("ACTIVE");
        registrationCommand.setMetadata(new HashMap<>());

        updateCommand = new ServiceUpdateCommand();
        updateCommand.setName("更新后的服务");
        updateCommand.setDescription("更新后的描述");
        updateCommand.setType("REST_API");
        updateCommand.setVersion("1.1.0");
        updateCommand.setEndpoint("https://api.example.com/v1.1");
        updateCommand.setGroupId("com.example");
        updateCommand.setArtifactId("test-service");
        updateCommand.setTags(Arrays.asList("api", "updated"));
        updateCommand.setStatus("ACTIVE");
        updateCommand.setMetadata(new HashMap<>());
    }

    @Test
    void testRegisterServiceSuccess() throws MalformedURLException {
        // Arrange
        when(serviceRegistrationDomainService.registerService(
                eq("测试服务"), eq("这是一个测试服务"), eq(ServiceType.REST_API), 
                any(Version.class), any(URL.class), eq("com.example"), eq("test-service"),
                any(Set.class), eq(ServiceStatus.ACTIVE), any(Metadata.class)
        )).thenReturn(testService);
        when(serviceRepository.save(testService)).thenReturn(testService);
        when(serviceAssembler.toDTO(testService)).thenReturn(testServiceDTO);

        // Act
        ServiceDTO result = serviceRegistryService.registerService(registrationCommand);

        // Assert
        assertNotNull(result);
        assertEquals(testServiceDTO.getId(), result.getId());
        assertEquals(testServiceDTO.getName(), result.getName());
        verify(serviceRegistrationDomainService).registerService(
                eq("测试服务"), eq("这是一个测试服务"), eq(ServiceType.REST_API), 
                any(Version.class), any(URL.class), eq("com.example"), eq("test-service"),
                any(Set.class), eq(ServiceStatus.ACTIVE), any(Metadata.class)
        );
        verify(serviceRepository).save(testService);
    }

    @Test
    void testRegisterServiceWithInvalidUrl() {
        // Arrange
        registrationCommand.setEndpoint("invalid-url");

        // Act & Assert
        assertThrows(ServiceRegistrationFailedException.class, () -> {
            serviceRegistryService.registerService(registrationCommand);
        });
    }

    @Test
    void testRegisterServiceWithDomainServiceException() throws MalformedURLException {
        // Arrange
        when(serviceRegistrationDomainService.registerService(
                anyString(), anyString(), any(ServiceType.class), 
                any(Version.class), any(URL.class), anyString(), anyString(),
                any(Set.class), any(ServiceStatus.class), any(Metadata.class)
        )).thenThrow(new IllegalArgumentException("Service name already exists"));

        // Act & Assert
        ServiceRegistrationFailedException exception = assertThrows(ServiceRegistrationFailedException.class, () -> {
            serviceRegistryService.registerService(registrationCommand);
        });
        assertTrue(exception.getMessage().contains("Service name already exists"));
    }

    @Test
    void testUpdateServiceSuccess() throws MalformedURLException {
        // Arrange
        String serviceId = testService.getId().getValue();
        Service updatedService = Service.create(
                "更新后的服务", "更新后的描述", ServiceType.REST_API, Version.of("1.1.0"),
                new URL("https://api.example.com/v1.1"), "com.example", "test-service",
                Set.of(Tag.of("api"), Tag.of("updated")), ServiceStatus.ACTIVE, Metadata.empty()
        );
        ServiceDTO updatedServiceDTO = new ServiceDTO();
        updatedServiceDTO.setId(updatedService.getId().getValue());
        updatedServiceDTO.setName(updatedService.getName());
        updatedServiceDTO.setDescription(updatedService.getDescription());

        when(serviceRepository.findById(serviceId)).thenReturn(testService);
        when(serviceRepository.save(any(Service.class))).thenReturn(updatedService);
        when(serviceAssembler.toDTO(updatedService)).thenReturn(updatedServiceDTO);

        // Act
        ServiceDTO result = serviceRegistryService.updateService(serviceId, updateCommand);

        // Assert
        assertNotNull(result);
        assertEquals(updatedServiceDTO.getId(), result.getId());
        assertEquals(updatedServiceDTO.getName(), result.getName());
        verify(serviceRepository).findById(serviceId);
        verify(serviceRepository).save(any(Service.class));
    }

    @Test
    void testUpdateServiceNotFound() {
        // Arrange
        String serviceId = "non-existent-id";
        when(serviceRepository.findById(serviceId)).thenReturn(null);

        // Act & Assert
        ServiceUpdateFailedException exception = assertThrows(ServiceUpdateFailedException.class, () -> {
            serviceRegistryService.updateService(serviceId, updateCommand);
        });
        assertTrue(exception.getMessage().contains("Service not found"));
    }

    @Test
    void testUpdateServiceWithInvalidUrl() {
        // Arrange
        String serviceId = testService.getId().getValue();
        updateCommand.setEndpoint("invalid-url");
        when(serviceRepository.findById(serviceId)).thenReturn(testService);

        // Act & Assert
        assertThrows(ServiceUpdateFailedException.class, () -> {
            serviceRegistryService.updateService(serviceId, updateCommand);
        });
    }

    @Test
    void testDeregisterServiceSuccess() {
        // Arrange
        String serviceId = testService.getId().getValue();
        when(serviceRepository.findById(serviceId)).thenReturn(testService);

        // Act
        serviceRegistryService.deregisterService(serviceId);

        // Assert
        verify(serviceRepository).findById(serviceId);
        verify(serviceRepository).delete(testService);
    }

    @Test
    void testDeregisterServiceNotFound() {
        // Arrange
        String serviceId = "non-existent-id";
        when(serviceRepository.findById(serviceId)).thenReturn(null);

        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            serviceRegistryService.deregisterService(serviceId);
        });
        assertTrue(exception.getMessage().contains("Service not found"));
    }

    @Test
    void testGetServiceByIdSuccess() {
        // Arrange
        String serviceId = testService.getId().getValue();
        when(serviceRepository.findById(serviceId)).thenReturn(testService);
        when(serviceAssembler.toDTO(testService)).thenReturn(testServiceDTO);

        // Act
        ServiceDTO result = serviceRegistryService.getServiceById(serviceId);

        // Assert
        assertNotNull(result);
        assertEquals(testServiceDTO.getId(), result.getId());
        assertEquals(testServiceDTO.getName(), result.getName());
        verify(serviceRepository).findById(serviceId);
    }

    @Test
    void testGetServiceByIdNotFound() {
        // Arrange
        String serviceId = "non-existent-id";
        when(serviceRepository.findById(serviceId)).thenReturn(null);

        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            serviceRegistryService.getServiceById(serviceId);
        });
        assertTrue(exception.getMessage().contains("Service not found"));
    }
}