package com.archscope.app.service.impl;

import com.archscope.app.assembler.ServiceAssembler;
import com.archscope.app.command.CapabilityRegistrationCommand;
import com.archscope.app.dto.CapabilityDTO;
import com.archscope.domain.model.servicediscovery.Capability;
import com.archscope.domain.model.servicediscovery.Service;
import com.archscope.domain.repository.CapabilityRepository;
import com.archscope.domain.repository.ServiceRepository;
import com.archscope.domain.service.CapabilityManagementDomainService;
import com.archscope.domain.valueobject.CapabilityId;
import com.archscope.domain.valueobject.ServiceId;
import com.archscope.domain.valueobject.Version;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * CapabilityManagementServiceImpl 单元测试
 */
@ExtendWith(MockitoExtension.class)
class CapabilityManagementServiceImplTest {

    // Test constants
    private static final String VALID_SERVICE_ID = "service-123";
    private static final String INVALID_SERVICE_ID = "non-existent-service";
    private static final String VALID_CAPABILITY_ID = "capability-123";
    private static final String INVALID_CAPABILITY_ID = "non-existent-capability";
    private static final String CAPABILITY_NAME = "test-capability";
    private static final String CAPABILITY_DESCRIPTION = "Test capability description";
    private static final String CAPABILITY_VERSION = "1.0.0";

    @Mock
    private CapabilityRepository capabilityRepository;

    @Mock
    private ServiceRepository serviceRepository;

    @Mock
    private CapabilityManagementDomainService capabilityManagementDomainService;

    @Mock
    private ServiceAssembler serviceAssembler;

    @Mock
    private Service mockService;

    @Mock
    private Capability mockCapability;

    @Mock
    private CapabilityDTO mockCapabilityDTO;

    private CapabilityManagementServiceImpl capabilityManagementService;

    @BeforeEach
    void setUp() {
        capabilityManagementService = new CapabilityManagementServiceImpl(
                capabilityRepository,
                serviceRepository,
                capabilityManagementDomainService,
                serviceAssembler
        );
    }

    // Test data builders
    private CapabilityRegistrationCommand createValidCapabilityCommand() {
        CapabilityRegistrationCommand command = new CapabilityRegistrationCommand();
        command.setName(CAPABILITY_NAME);
        command.setDescription(CAPABILITY_DESCRIPTION);
        command.setVersion(CAPABILITY_VERSION);
        return command;
    }

    private CapabilityRegistrationCommand createMinimalCapabilityCommand() {
        CapabilityRegistrationCommand command = new CapabilityRegistrationCommand();
        command.setName(CAPABILITY_NAME);
        command.setVersion(CAPABILITY_VERSION);
        return command;
    }

    @Test
    void registerCapability_ShouldReturnCapabilityDTO_WhenValidInput() {
        // Given
        CapabilityRegistrationCommand command = createValidCapabilityCommand();
        ServiceId domainServiceId = ServiceId.of(VALID_SERVICE_ID);
        
        when(serviceRepository.findById(domainServiceId)).thenReturn(mockService);
        when(capabilityManagementDomainService.registerCapability(
                eq(domainServiceId), eq(CAPABILITY_NAME), eq(CAPABILITY_DESCRIPTION),
                any(Version.class), anySet(), anySet())).thenReturn(mockCapability);
        when(capabilityRepository.save(mockCapability)).thenReturn(mockCapability);
        when(serviceAssembler.toCapabilityDTO(mockCapability)).thenReturn(mockCapabilityDTO);

        // When
        CapabilityDTO result = capabilityManagementService.registerCapability(VALID_SERVICE_ID, command);

        // Then
        assertNotNull(result);
        assertEquals(mockCapabilityDTO, result);
        
        // Verify interactions
        verify(serviceRepository).findById(domainServiceId);
        verify(capabilityManagementDomainService).registerCapability(
                eq(domainServiceId), eq(CAPABILITY_NAME), eq(CAPABILITY_DESCRIPTION),
                any(Version.class), anySet(), anySet());
        verify(capabilityRepository).save(mockCapability);
        verify(serviceAssembler).toCapabilityDTO(mockCapability);
    }

    @Test
    void registerCapability_ShouldThrowException_WhenServiceNotFound() {
        // Given
        CapabilityRegistrationCommand command = createMinimalCapabilityCommand();
        ServiceId domainServiceId = ServiceId.of(INVALID_SERVICE_ID);
        when(serviceRepository.findById(domainServiceId)).thenReturn(null);

        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> capabilityManagementService.registerCapability(INVALID_SERVICE_ID, command)
        );
        assertEquals("Service not found with ID: " + INVALID_SERVICE_ID, exception.getMessage());
    }

    @Test
    void getServiceCapabilities_ShouldReturnCapabilityList_WhenValidServiceId() {
        // Given
        ServiceId domainServiceId = ServiceId.of(VALID_SERVICE_ID);
        List<Capability> capabilities = Arrays.asList(mockCapability);

        when(capabilityRepository.findByServiceId(domainServiceId)).thenReturn(capabilities);
        when(serviceAssembler.toCapabilityDTO(mockCapability)).thenReturn(mockCapabilityDTO);

        // When
        List<CapabilityDTO> result = capabilityManagementService.getServiceCapabilities(VALID_SERVICE_ID);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(mockCapabilityDTO, result.get(0));
        verify(capabilityRepository).findByServiceId(domainServiceId);
    }

    @Test
    void getServiceCapabilities_ShouldReturnEmptyList_WhenBlankServiceId() {
        // When
        List<CapabilityDTO> result = capabilityManagementService.getServiceCapabilities("");

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verifyNoInteractions(capabilityRepository);
    }

    @Test
    void deprecateCapability_ShouldMarkAsDeprecated_WhenValidCapabilityId() {
        // Given
        CapabilityId domainCapabilityId = CapabilityId.of(VALID_CAPABILITY_ID);

        when(capabilityRepository.findById(domainCapabilityId)).thenReturn(mockCapability);
        when(capabilityRepository.save(mockCapability)).thenReturn(mockCapability);

        // When
        capabilityManagementService.deprecateCapability(VALID_CAPABILITY_ID);

        // Then
        verify(capabilityRepository).findById(domainCapabilityId);
        verify(mockCapability).markAsDeprecated();
        verify(capabilityRepository).save(mockCapability);
    }

    @Test
    void deprecateCapability_ShouldThrowException_WhenCapabilityNotFound() {
        // Given
        CapabilityId domainCapabilityId = CapabilityId.of(INVALID_CAPABILITY_ID);

        when(capabilityRepository.findById(domainCapabilityId)).thenReturn(null);

        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> capabilityManagementService.deprecateCapability(INVALID_CAPABILITY_ID)
        );
        assertEquals("Capability not found with ID: " + INVALID_CAPABILITY_ID, exception.getMessage());
    }
}