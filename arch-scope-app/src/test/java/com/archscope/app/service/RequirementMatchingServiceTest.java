package com.archscope.app.service;

import com.archscope.app.assembler.RequirementAssembler;
import com.archscope.app.command.RecommendationFeedbackCommand;
import com.archscope.app.command.RequirementMatchingCommand;
import com.archscope.app.dto.FeedbackDTO;
import com.archscope.app.dto.RequirementDTO;
import com.archscope.app.dto.ServiceRecommendationDTO;
import com.archscope.domain.model.servicediscovery.Requirement;
import com.archscope.domain.model.servicediscovery.RequirementPriority;
import com.archscope.domain.repository.RequirementRepository;
import com.archscope.domain.service.RequirementMatchingDomainService;
import com.archscope.domain.valueobject.RequirementId;
import com.archscope.domain.valueobject.ServiceId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 需求匹配应用服务测试
 */
class RequirementMatchingServiceTest {

    @Mock
    private RequirementRepository requirementRepository;

    @Mock
    private RequirementMatchingDomainService requirementMatchingDomainService;

    @Mock
    private RequirementAssembler requirementAssembler;

    private RequirementMatchingService requirementMatchingService;

    private Requirement testRequirement;
    private RequirementDTO testRequirementDTO;
    private RequirementMatchingCommand matchingCommand;
    private ServiceId testServiceId;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        
        // 创建测试数据
        testRequirement = Requirement.create(
                "需要一个用户管理系统",
                Arrays.asList("用户认证", "用户注册", "密码重置"),
                RequirementPriority.HIGH
        );

        testRequirementDTO = new RequirementDTO();
        testRequirementDTO.setId(testRequirement.getId().getValue());
        testRequirementDTO.setDescription(testRequirement.getDescription());
        testRequirementDTO.setRequiredCapabilities(testRequirement.getRequiredCapabilities());
        testRequirementDTO.setPriority(testRequirement.getPriority().name());

        matchingCommand = new RequirementMatchingCommand();
        matchingCommand.setDescription("需要一个用户管理系统");
        matchingCommand.setRequiredCapabilities(Arrays.asList("用户认证", "用户注册", "密码重置"));
        matchingCommand.setPriority("HIGH");

        testServiceId = ServiceId.createNew();
    }

    @Test
    void testFindServicesForRequirementWithCommand() {
        // Arrange
        List<ServiceId> matchingServiceIds = Arrays.asList(testServiceId);
        Map<ServiceId, Integer> recommendationScores = new HashMap<>();
        recommendationScores.put(testServiceId, 85);

        when(requirementAssembler.toDomain(matchingCommand)).thenReturn(testRequirement);
        when(requirementMatchingDomainService.findMatchingServices(testRequirement)).thenReturn(matchingServiceIds);
        when(requirementMatchingDomainService.getServiceRecommendationScores(matchingServiceIds))
                .thenReturn(recommendationScores);

        // Act
        List<ServiceRecommendationDTO> result = requirementMatchingService.findServicesForRequirement(matchingCommand);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        ServiceRecommendationDTO recommendation = result.get(0);
        assertEquals(testServiceId.getValue(), recommendation.getServiceId());
        assertEquals(85, recommendation.getScore());
        verify(requirementMatchingDomainService).findMatchingServices(testRequirement);
    }

    @Test
    void testFindServicesForRequirementWithDTO() {
        // Arrange
        List<ServiceId> matchingServiceIds = Arrays.asList(testServiceId);
        Map<ServiceId, Integer> recommendationScores = new HashMap<>();
        recommendationScores.put(testServiceId, 90);

        when(requirementAssembler.toDomain(testRequirementDTO)).thenReturn(testRequirement);
        when(requirementMatchingDomainService.findMatchingServices(testRequirement)).thenReturn(matchingServiceIds);
        when(requirementMatchingDomainService.getServiceRecommendationScores(matchingServiceIds))
                .thenReturn(recommendationScores);

        // Act
        List<ServiceRecommendationDTO> result = requirementMatchingService.findServicesForRequirement(testRequirementDTO);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        ServiceRecommendationDTO recommendation = result.get(0);
        assertEquals(testServiceId.getValue(), recommendation.getServiceId());
        assertEquals(90, recommendation.getScore());
    }

    @Test
    void testFindServicesForRequirementEmptyResult() {
        // Arrange
        when(requirementAssembler.toDomain(matchingCommand)).thenReturn(testRequirement);
        when(requirementMatchingDomainService.findMatchingServices(testRequirement)).thenReturn(Collections.emptyList());
        when(requirementMatchingDomainService.getServiceRecommendationScores(Collections.emptyList()))
                .thenReturn(Collections.emptyMap());

        // Act
        List<ServiceRecommendationDTO> result = requirementMatchingService.findServicesForRequirement(matchingCommand);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testRecordRecommendationFeedbackWithCommand() {
        // Arrange
        RecommendationFeedbackCommand feedbackCommand = new RecommendationFeedbackCommand();
        feedbackCommand.setRequirementId(testRequirement.getId().getValue());
        feedbackCommand.setServiceId(testServiceId.getValue());
        feedbackCommand.setSatisfied(true);
        feedbackCommand.setFeedback("服务很好用");

        FeedbackDTO expectedFeedback = new FeedbackDTO();
        expectedFeedback.setRequirementId(testRequirement.getId().getValue());
        expectedFeedback.setServiceId(testServiceId.getValue());
        expectedFeedback.setSatisfied(true);
        expectedFeedback.setFeedback("服务很好用");

        // Act
        FeedbackDTO result = requirementMatchingService.recordRecommendationFeedback(feedbackCommand);

        // Assert
        assertNotNull(result);
        assertEquals(expectedFeedback.getRequirementId(), result.getRequirementId());
        assertEquals(expectedFeedback.getServiceId(), result.getServiceId());
        assertEquals(expectedFeedback.isSatisfied(), result.isSatisfied());
        assertEquals(expectedFeedback.getFeedback(), result.getFeedback());
        
        verify(requirementMatchingDomainService).recordMatchingFeedback(
                RequirementId.of(testRequirement.getId().getValue()),
                testServiceId,
                true,
                "服务很好用"
        );
    }

    @Test
    void testRecordRecommendationFeedbackWithRecommendationId() {
        // Arrange
        String recommendationId = "recommendation-123";
        FeedbackDTO feedback = new FeedbackDTO();
        feedback.setRequirementId(testRequirement.getId().getValue());
        feedback.setServiceId(testServiceId.getValue());
        feedback.setSatisfied(false);
        feedback.setFeedback("服务不符合需求");

        // Act
        FeedbackDTO result = requirementMatchingService.recordRecommendationFeedback(recommendationId, feedback);

        // Assert
        assertNotNull(result);
        assertEquals(feedback.getRequirementId(), result.getRequirementId());
        assertEquals(feedback.getServiceId(), result.getServiceId());
        assertEquals(feedback.isSatisfied(), result.isSatisfied());
        assertEquals(feedback.getFeedback(), result.getFeedback());
        
        verify(requirementMatchingDomainService).recordMatchingFeedback(
                RequirementId.of(testRequirement.getId().getValue()),
                ServiceId.of(testServiceId.getValue()),
                false,
                "服务不符合需求"
        );
    }

    @Test
    void testGenerateCapabilityRequirements() {
        // Arrange
        String description = "需要一个用户管理系统，包含登录和注册功能";
        List<String> expectedCapabilities = Arrays.asList("用户认证", "用户注册");
        
        when(requirementMatchingDomainService.generateCapabilityRequirements(description))
                .thenReturn(expectedCapabilities);

        // Act
        List<String> result = requirementMatchingService.generateCapabilityRequirements(description);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.contains("用户认证"));
        assertTrue(result.contains("用户注册"));
        verify(requirementMatchingDomainService).generateCapabilityRequirements(description);
    }

    @Test
    void testGenerateCapabilityRequirementsEmptyDescription() {
        // Arrange
        String description = "";
        when(requirementMatchingDomainService.generateCapabilityRequirements(description))
                .thenReturn(Collections.emptyList());

        // Act
        List<String> result = requirementMatchingService.generateCapabilityRequirements(description);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testGetServiceRecommendations() {
        // Arrange
        ServiceId serviceId1 = ServiceId.createNew();
        ServiceId serviceId2 = ServiceId.createNew();
        List<String> serviceIds = Arrays.asList(serviceId1.getValue(), serviceId2.getValue());
        
        Map<ServiceId, Integer> scores = new HashMap<>();
        scores.put(serviceId1, 95);
        scores.put(serviceId2, 80);
        
        when(requirementMatchingDomainService.getServiceRecommendationScores(anyList()))
                .thenReturn(scores);

        // Act
        List<ServiceRecommendationDTO> result = requirementMatchingService.getServiceRecommendations(serviceIds);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        
        // 验证结果按分数降序排列
        assertTrue(result.get(0).getScore() >= result.get(1).getScore());
        
        // 验证包含所有服务
        Set<String> resultServiceIds = new HashSet<>();
        for (ServiceRecommendationDTO recommendation : result) {
            resultServiceIds.add(recommendation.getServiceId());
        }
        assertTrue(resultServiceIds.contains(serviceId1.getValue()));
        assertTrue(resultServiceIds.contains(serviceId2.getValue()));
    }

    @Test
    void testGetServiceRecommendationsEmptyList() {
        // Arrange
        List<String> serviceIds = Collections.emptyList();
        when(requirementMatchingDomainService.getServiceRecommendationScores(anyList()))
                .thenReturn(Collections.emptyMap());

        // Act
        List<ServiceRecommendationDTO> result = requirementMatchingService.getServiceRecommendations(serviceIds);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }
}