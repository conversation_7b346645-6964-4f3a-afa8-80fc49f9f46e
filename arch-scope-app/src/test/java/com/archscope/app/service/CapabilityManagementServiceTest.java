package com.archscope.app.service;

import com.archscope.app.assembler.CapabilityAssembler;
import com.archscope.app.assembler.ServiceAssembler;
import com.archscope.app.command.CapabilityRegistrationCommand;
import com.archscope.app.dto.CapabilityDTO;
import com.archscope.app.dto.ServiceDTO;
import com.archscope.app.exception.CapabilityRegistrationFailedException;
import com.archscope.domain.model.servicediscovery.Capability;
import com.archscope.domain.model.servicediscovery.CapabilityExample;
import com.archscope.domain.model.servicediscovery.Service;
import com.archscope.domain.model.servicediscovery.ServiceStatus;
import com.archscope.domain.model.servicediscovery.ServiceType;
import com.archscope.domain.repository.CapabilityRepository;
import com.archscope.domain.repository.ServiceRepository;
import com.archscope.domain.service.CapabilityManagementDomainService;
import com.archscope.domain.valueobject.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 能力管理应用服务测试
 */
class CapabilityManagementServiceTest {

    @Mock
    private CapabilityRepository capabilityRepository;

    @Mock
    private ServiceRepository serviceRepository;

    @Mock
    private CapabilityManagementDomainService capabilityManagementDomainService;

    @Mock
    private CapabilityAssembler capabilityAssembler;

    @Mock
    private ServiceAssembler serviceAssembler;

    private CapabilityManagementService capabilityManagementService;

    private Service testService;
    private Capability testCapability;
    private ServiceDTO testServiceDTO;
    private CapabilityDTO testCapabilityDTO;
    private CapabilityRegistrationCommand registrationCommand;

    @BeforeEach
    void setUp() throws MalformedURLException {
        MockitoAnnotations.openMocks(this);
        
        // 创建测试数据
        testService = Service.create(
                "测试服务",
                "这是一个测试服务",
                ServiceType.REST_API,
                Version.of("1.0.0"),
                new URL("https://api.example.com/v1"),
                "com.example",
                "test-service",
                Collections.singleton(Tag.of("api")),
                ServiceStatus.ACTIVE,
                Metadata.empty()
        );

        Set<CapabilityExample> examples = new HashSet<>();
        examples.add(new CapabilityExample("登录示例", "用户登录示例", 
                "{\"username\":\"user\",\"password\":\"pass\"}", 
                "{\"token\":\"jwt_token\",\"expires\":3600}"));

        testCapability = Capability.create(
                testService.getId(),
                "用户认证",
                "提供用户认证功能",
                Version.of("1.0.0"),
                examples,
                Collections.singleton(Tag.of("authentication"))
        );

        testServiceDTO = new ServiceDTO();
        testServiceDTO.setId(testService.getId().getValue());
        testServiceDTO.setName(testService.getName());

        testCapabilityDTO = new CapabilityDTO();
        testCapabilityDTO.setId(testCapability.getId().getValue());
        testCapabilityDTO.setServiceId(testCapability.getServiceId().getValue());
        testCapabilityDTO.setName(testCapability.getName());
        testCapabilityDTO.setDescription(testCapability.getDescription());
        testCapabilityDTO.setVersion(testCapability.getVersion().getValue());
        testCapabilityDTO.setTags(Arrays.asList("authentication"));
        testCapabilityDTO.setDeprecated(testCapability.isDeprecated());

        registrationCommand = new CapabilityRegistrationCommand();
        registrationCommand.setName("用户认证");
        registrationCommand.setDescription("提供用户认证功能");
        registrationCommand.setVersion("1.0.0");
        registrationCommand.setTags(Arrays.asList("authentication"));
    }

    @Test
    void testRegisterCapabilitySuccess() {
        // Arrange
        String serviceId = testService.getId().getValue();
        when(capabilityManagementDomainService.registerCapability(
                any(ServiceId.class), eq("用户认证"), eq("提供用户认证功能"),
                any(Version.class), any(Set.class), any(Set.class)
        )).thenReturn(testCapability);
        when(capabilityRepository.save(testCapability)).thenReturn(testCapability);
        when(capabilityAssembler.toDTO(testCapability)).thenReturn(testCapabilityDTO);

        // Act
        CapabilityDTO result = capabilityManagementService.registerCapability(serviceId, registrationCommand);

        // Assert
        assertNotNull(result);
        assertEquals(testCapabilityDTO.getId(), result.getId());
        assertEquals(testCapabilityDTO.getName(), result.getName());
        verify(capabilityManagementDomainService).registerCapability(
                any(ServiceId.class), eq("用户认证"), eq("提供用户认证功能"),
                any(Version.class), any(Set.class), any(Set.class)
        );
        verify(capabilityRepository).save(testCapability);
    }

    @Test
    void testRegisterCapabilityWithDomainServiceException() {
        // Arrange
        String serviceId = testService.getId().getValue();
        when(capabilityManagementDomainService.registerCapability(
                any(ServiceId.class), anyString(), anyString(),
                any(Version.class), any(Set.class), any(Set.class)
        )).thenThrow(new IllegalArgumentException("Capability name already exists"));

        // Act & Assert
        CapabilityRegistrationFailedException exception = assertThrows(CapabilityRegistrationFailedException.class, () -> {
            capabilityManagementService.registerCapability(serviceId, registrationCommand);
        });
        assertTrue(exception.getMessage().contains("Capability name already exists"));
    }

    @Test
    void testGetServiceCapabilities() {
        // Arrange
        String serviceId = testService.getId().getValue();
        List<Capability> capabilities = Arrays.asList(testCapability);
        when(capabilityRepository.findByServiceId(ServiceId.of(serviceId))).thenReturn(capabilities);
        when(capabilityAssembler.toDTO(testCapability)).thenReturn(testCapabilityDTO);

        // Act
        List<CapabilityDTO> result = capabilityManagementService.getServiceCapabilities(serviceId);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(testCapabilityDTO.getId(), result.get(0).getId());
        assertEquals(testCapabilityDTO.getName(), result.get(0).getName());
    }

    @Test
    void testFindServicesByCapabilityRequirements() {
        // Arrange
        List<String> requiredCapabilities = Arrays.asList("用户认证", "权限管理");
        List<ServiceId> serviceIds = Arrays.asList(testService.getId());
        List<Service> services = Arrays.asList(testService);
        
        when(capabilityManagementDomainService.findServicesByCapabilityRequirements(requiredCapabilities))
                .thenReturn(serviceIds);
        when(serviceRepository.findByIds(serviceIds)).thenReturn(services);
        when(serviceAssembler.toDTO(testService)).thenReturn(testServiceDTO);

        // Act
        List<ServiceDTO> result = capabilityManagementService.findServicesByCapabilityRequirements(requiredCapabilities);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(testServiceDTO.getId(), result.get(0).getId());
    }

    @Test
    void testGetCapabilityByIdSuccess() {
        // Arrange
        String capabilityId = testCapability.getId().getValue();
        when(capabilityRepository.findById(CapabilityId.of(capabilityId))).thenReturn(testCapability);
        when(capabilityAssembler.toDTO(testCapability)).thenReturn(testCapabilityDTO);

        // Act
        CapabilityDTO result = capabilityManagementService.getCapabilityById(capabilityId);

        // Assert
        assertNotNull(result);
        assertEquals(testCapabilityDTO.getId(), result.getId());
        assertEquals(testCapabilityDTO.getName(), result.getName());
    }

    @Test
    void testGetCapabilityByIdNotFound() {
        // Arrange
        String capabilityId = "non-existent-id";
        when(capabilityRepository.findById(CapabilityId.of(capabilityId))).thenReturn(null);

        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            capabilityManagementService.getCapabilityById(capabilityId);
        });
        assertTrue(exception.getMessage().contains("Capability not found"));
    }

    @Test
    void testUpdateCapabilitySuccess() {
        // Arrange
        String capabilityId = testCapability.getId().getValue();
        CapabilityRegistrationCommand updateCommand = new CapabilityRegistrationCommand();
        updateCommand.setName("更新后的能力");
        updateCommand.setDescription("更新后的描述");
        updateCommand.setVersion("1.1.0");
        updateCommand.setTags(Arrays.asList("authentication", "updated"));

        when(capabilityRepository.findById(CapabilityId.of(capabilityId))).thenReturn(testCapability);
        when(capabilityRepository.save(any(Capability.class))).thenReturn(testCapability);
        when(capabilityAssembler.toDTO(any(Capability.class))).thenReturn(testCapabilityDTO);

        // Act
        CapabilityDTO result = capabilityManagementService.updateCapability(capabilityId, updateCommand);

        // Assert
        assertNotNull(result);
        verify(capabilityRepository).findById(CapabilityId.of(capabilityId));
        verify(capabilityRepository).save(any(Capability.class));
    }

    @Test
    void testUpdateCapabilityNotFound() {
        // Arrange
        String capabilityId = "non-existent-id";
        CapabilityRegistrationCommand updateCommand = new CapabilityRegistrationCommand();
        when(capabilityRepository.findById(CapabilityId.of(capabilityId))).thenReturn(null);

        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            capabilityManagementService.updateCapability(capabilityId, updateCommand);
        });
        assertTrue(exception.getMessage().contains("Capability not found"));
    }

    @Test
    void testDeprecateCapabilitySuccess() {
        // Arrange
        String capabilityId = testCapability.getId().getValue();
        when(capabilityRepository.findById(CapabilityId.of(capabilityId))).thenReturn(testCapability);

        // Act
        capabilityManagementService.deprecateCapability(capabilityId);

        // Assert
        verify(capabilityRepository).findById(CapabilityId.of(capabilityId));
        verify(capabilityRepository).save(testCapability);
        assertTrue(testCapability.isDeprecated());
    }

    @Test
    void testDeprecateCapabilityNotFound() {
        // Arrange
        String capabilityId = "non-existent-id";
        when(capabilityRepository.findById(CapabilityId.of(capabilityId))).thenReturn(null);

        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            capabilityManagementService.deprecateCapability(capabilityId);
        });
        assertTrue(exception.getMessage().contains("Capability not found"));
    }

    @Test
    void testDeleteCapabilitySuccess() {
        // Arrange
        String capabilityId = testCapability.getId().getValue();
        when(capabilityRepository.findById(CapabilityId.of(capabilityId))).thenReturn(testCapability);

        // Act
        capabilityManagementService.deleteCapability(capabilityId);

        // Assert
        verify(capabilityRepository).findById(CapabilityId.of(capabilityId));
        verify(capabilityRepository).delete(testCapability);
    }

    @Test
    void testDeleteCapabilityNotFound() {
        // Arrange
        String capabilityId = "non-existent-id";
        when(capabilityRepository.findById(CapabilityId.of(capabilityId))).thenReturn(null);

        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            capabilityManagementService.deleteCapability(capabilityId);
        });
        assertTrue(exception.getMessage().contains("Capability not found"));
    }
}