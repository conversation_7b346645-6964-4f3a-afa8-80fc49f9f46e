package com.archscope.app.service;

import com.archscope.app.assembler.ServiceAssembler;
import com.archscope.app.dto.ServiceDTO;
import com.archscope.app.dto.ServiceQueryRequest;
import com.archscope.app.service.impl.ServiceDiscoveryServiceImpl;
import com.archscope.domain.model.servicediscovery.Service;
import com.archscope.domain.model.servicediscovery.ServiceStatus;
import com.archscope.domain.model.servicediscovery.ServiceType;
import com.archscope.domain.repository.ServiceRepository;
import com.archscope.domain.valueobject.*;
import com.archscope.facade.dto.PageResponseDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

/**
 * 服务发现应用服务测试
 */
class ServiceDiscoveryServiceTest {

    @Mock
    private ServiceRepository serviceRepository;

    @Mock
    private ServiceAssembler serviceAssembler;

    private ServiceDiscoveryService serviceDiscoveryService;

    private Service testService;
    private ServiceDTO testServiceDTO;

    @BeforeEach
    void setUp() throws MalformedURLException {
        MockitoAnnotations.openMocks(this);
        serviceDiscoveryService = new ServiceDiscoveryServiceImpl(serviceRepository, serviceAssembler);

        // 创建测试数据
        testService = Service.create(
                "测试服务",
                "这是一个测试服务",
                ServiceType.REST_API,
                Version.of("1.0.0"),
                new URL("https://api.example.com/v1"),
                "com.example",
                "test-service",
                Collections.singleton(Tag.of("api")),
                ServiceStatus.ACTIVE,
                Metadata.empty()
        );

        testServiceDTO = new ServiceDTO();
        testServiceDTO.setId(testService.getId().getValue());
        testServiceDTO.setName(testService.getName());
        testServiceDTO.setDescription(testService.getDescription());
        testServiceDTO.setType(testService.getType().name());
        testServiceDTO.setVersion(testService.getVersion().getValue());
        testServiceDTO.setEndpoint(testService.getEndpoint().toString());
        testServiceDTO.setGroupId(testService.getGroupId());
        testServiceDTO.setArtifactId(testService.getArtifactId());
        testServiceDTO.setStatus(testService.getStatus().name());
        testServiceDTO.setTags(Arrays.asList("api"));
        testServiceDTO.setMetadata(new HashMap<>());
        testServiceDTO.setCapabilities(new ArrayList<>());
        testServiceDTO.setRegisteredAt(testService.getRegisteredAt());
        testServiceDTO.setLastUpdatedAt(testService.getLastUpdatedAt());
    }

    @Test
    void testFindAllActiveServices() {
        // Arrange
        List<Service> services = Arrays.asList(testService);
        when(serviceRepository.findByStatus("ACTIVE")).thenReturn(services);
        when(serviceAssembler.toDTO(testService)).thenReturn(testServiceDTO);

        // Act
        List<ServiceDTO> result = serviceDiscoveryService.findAllActiveServices();

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(testServiceDTO.getId(), result.get(0).getId());
        assertEquals(testServiceDTO.getName(), result.get(0).getName());
    }

    @Test
    void testFindServices() {
        // Arrange
        PageResult<Service> pageResult = new PageResult<>(
                Arrays.asList(testService), 0, 10, 1
        );
        when(serviceRepository.findAll(any(PageRequest.class))).thenReturn(pageResult);
        when(serviceAssembler.toDTO(testService)).thenReturn(testServiceDTO);

        // Act
        PageResponseDTO<ServiceDTO> result = serviceDiscoveryService.findServices(0, 10);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getContent().size());
        assertEquals(0, result.getPage());
        assertEquals(10, result.getSize());
        assertEquals(1, result.getTotalElements());
        assertEquals(1, result.getTotalPages());
        assertTrue(result.isFirst());
        assertTrue(result.isLast());
    }

    @Test
    void testFindServicesWithSort() {
        // Arrange
        PageResult<Service> pageResult = new PageResult<>(
                Arrays.asList(testService), 0, 10, 1
        );
        when(serviceRepository.findAll(any(PageRequest.class))).thenReturn(pageResult);
        when(serviceAssembler.toDTO(testService)).thenReturn(testServiceDTO);

        // Act
        PageResponseDTO<ServiceDTO> result = serviceDiscoveryService.findServicesWithSort(0, 10, "name", "ASC");

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getContent().size());
        assertEquals(testServiceDTO.getId(), result.getContent().get(0).getId());
    }

    @Test
    void testFindServicesByName() {
        // Arrange
        List<Service> services = Arrays.asList(testService);
        when(serviceRepository.findByNameContainingIgnoreCase("测试")).thenReturn(services);
        when(serviceAssembler.toDTO(testService)).thenReturn(testServiceDTO);

        // Act
        List<ServiceDTO> result = serviceDiscoveryService.findServicesByName("测试");

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(testServiceDTO.getName(), result.get(0).getName());
    }

    @Test
    void testFindServicesByType() {
        // Arrange
        List<Service> services = Arrays.asList(testService);
        when(serviceRepository.findByType("REST_API")).thenReturn(services);
        when(serviceAssembler.toDTO(testService)).thenReturn(testServiceDTO);

        // Act
        List<ServiceDTO> result = serviceDiscoveryService.findServicesByType("REST_API");

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(testServiceDTO.getType(), result.get(0).getType());
    }

    @Test
    void testFindServicesByStatus() {
        // Arrange
        List<Service> services = Arrays.asList(testService);
        when(serviceRepository.findByStatus("ACTIVE")).thenReturn(services);
        when(serviceAssembler.toDTO(testService)).thenReturn(testServiceDTO);

        // Act
        List<ServiceDTO> result = serviceDiscoveryService.findServicesByStatus("ACTIVE");

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(testServiceDTO.getStatus(), result.get(0).getStatus());
    }

    @Test
    void testFindServicesByTags() {
        // Arrange
        List<String> tags = Arrays.asList("api", "microservice");
        List<Service> services = Arrays.asList(testService);
        when(serviceRepository.findByTagsIn(tags)).thenReturn(services);
        when(serviceAssembler.toDTO(testService)).thenReturn(testServiceDTO);

        // Act
        List<ServiceDTO> result = serviceDiscoveryService.findServicesByTags(tags);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.get(0).getTags().contains("api"));
    }

    @Test
    void testFindServicesByCapability() {
        // Arrange
        List<Service> services = Arrays.asList(testService);
        when(serviceRepository.findByCapabilityName("用户认证")).thenReturn(services);
        when(serviceAssembler.toDTO(testService)).thenReturn(testServiceDTO);

        // Act
        List<ServiceDTO> result = serviceDiscoveryService.findServicesByCapability("用户认证");

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(testServiceDTO.getId(), result.get(0).getId());
    }

    @Test
    void testFindServicesByMavenCoordinates() {
        // Arrange
        List<Service> services = Arrays.asList(testService);
        when(serviceRepository.findByGroupIdAndArtifactId("com.example", "test-service")).thenReturn(services);
        when(serviceAssembler.toDTO(testService)).thenReturn(testServiceDTO);

        // Act
        List<ServiceDTO> result = serviceDiscoveryService.findServicesByMavenCoordinates("com.example", "test-service");

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(testServiceDTO.getGroupId(), result.get(0).getGroupId());
        assertEquals(testServiceDTO.getArtifactId(), result.get(0).getArtifactId());
    }

    @Test
    void testFindServiceByExactMavenCoordinates() {
        // Arrange
        when(serviceRepository.findByGroupIdAndArtifactIdAndVersion("com.example", "test-service", "1.0.0"))
                .thenReturn(testService);
        when(serviceAssembler.toDTO(testService)).thenReturn(testServiceDTO);

        // Act
        ServiceDTO result = serviceDiscoveryService.findServiceByExactMavenCoordinates("com.example", "test-service", "1.0.0");

        // Assert
        assertNotNull(result);
        assertEquals(testServiceDTO.getGroupId(), result.getGroupId());
        assertEquals(testServiceDTO.getArtifactId(), result.getArtifactId());
        assertEquals(testServiceDTO.getVersion(), result.getVersion());
    }

    @Test
    void testFindServiceByExactMavenCoordinatesNotFound() {
        // Arrange
        when(serviceRepository.findByGroupIdAndArtifactIdAndVersion("com.example", "not-found", "1.0.0"))
                .thenReturn(null);

        // Act
        ServiceDTO result = serviceDiscoveryService.findServiceByExactMavenCoordinates("com.example", "not-found", "1.0.0");

        // Assert
        assertNull(result);
    }

    @Test
    void testFindServicesByCriteria() {
        // Arrange
        PageResult<Service> pageResult = new PageResult<>(
                Arrays.asList(testService), 0, 10, 1
        );
        when(serviceRepository.findByCriteria(anyString(), anyString(), anyString(), anyList(), 
                anyString(), anyString(), anyString(), any(PageRequest.class))).thenReturn(pageResult);
        when(serviceAssembler.toDTO(testService)).thenReturn(testServiceDTO);

        // Act
        PageResponseDTO<ServiceDTO> result = serviceDiscoveryService.findServicesByCriteria(
                "测试", "REST_API", "ACTIVE", Arrays.asList("api"), 0, 10);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getContent().size());
        assertEquals(testServiceDTO.getId(), result.getContent().get(0).getId());
    }

    @Test
    void testFindServicesByCriteriaWithQueryRequest() {
        // Arrange
        ServiceQueryRequest queryRequest = ServiceQueryRequest.builder()
                .name("测试")
                .type("REST_API")
                .status("ACTIVE")
                .tags(Arrays.asList("api"))
                .page(0)
                .size(10)
                .sortBy("name")
                .sortDirection("ASC")
                .build();

        PageResult<Service> pageResult = new PageResult<>(
                Arrays.asList(testService), 0, 10, 1
        );
        when(serviceRepository.findByCriteria(anyString(), anyString(), anyString(), anyList(), 
                anyString(), anyString(), anyString(), any(PageRequest.class))).thenReturn(pageResult);
        when(serviceAssembler.toDTO(testService)).thenReturn(testServiceDTO);

        // Act
        PageResponseDTO<ServiceDTO> result = serviceDiscoveryService.findServicesByCriteria(queryRequest);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getContent().size());
        assertEquals(testServiceDTO.getId(), result.getContent().get(0).getId());
    }

    @Test
    void testGetServiceById() {
        // Arrange
        String serviceId = testService.getId().getValue();
        when(serviceRepository.findById(serviceId)).thenReturn(testService);
        when(serviceAssembler.toDTO(testService)).thenReturn(testServiceDTO);

        // Act
        ServiceDTO result = serviceDiscoveryService.getServiceById(serviceId);

        // Assert
        assertNotNull(result);
        assertEquals(testServiceDTO.getId(), result.getId());
        assertEquals(testServiceDTO.getName(), result.getName());
    }

    @Test
    void testGetServiceByIdNotFound() {
        // Arrange
        String serviceId = "non-existent-id";
        when(serviceRepository.findById(serviceId)).thenReturn(null);

        // Act
        ServiceDTO result = serviceDiscoveryService.getServiceById(serviceId);

        // Assert
        assertNull(result);
    }

    @Test
    void testGetServiceStatistics() {
        // Arrange
        when(serviceRepository.count()).thenReturn(100L);
        when(serviceRepository.countByStatus("ACTIVE")).thenReturn(80L);
        
        Map<String, Long> statusStats = new HashMap<>();
        statusStats.put("ACTIVE", 80L);
        statusStats.put("INACTIVE", 20L);
        when(serviceRepository.countByStatusGrouped()).thenReturn(statusStats);
        
        Map<String, Long> typeStats = new HashMap<>();
        typeStats.put("REST_API", 60L);
        typeStats.put("GRAPHQL_API", 40L);
        when(serviceRepository.countByTypeGrouped()).thenReturn(typeStats);
        
        when(serviceRepository.countRecentServices(7)).thenReturn(15L);

        // Act
        Object result = serviceDiscoveryService.getServiceStatistics();

        // Assert
        assertNotNull(result);
        assertTrue(result instanceof Map);
        
        @SuppressWarnings("unchecked")
        Map<String, Object> statistics = (Map<String, Object>) result;
        
        assertEquals(100L, statistics.get("totalServices"));
        assertEquals(80L, statistics.get("activeServices"));
        assertEquals(statusStats, statistics.get("statusStatistics"));
        assertEquals(typeStats, statistics.get("typeStatistics"));
        assertEquals(15L, statistics.get("recentServices"));
    }
}