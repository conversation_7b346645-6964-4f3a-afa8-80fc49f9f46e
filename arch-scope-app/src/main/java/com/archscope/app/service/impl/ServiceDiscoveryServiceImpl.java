package com.archscope.app.service.impl;

import com.archscope.app.dto.ServiceDTO;
import com.archscope.app.dto.ServiceQueryRequest;
import com.archscope.app.service.ServiceDiscoveryService;
import com.archscope.domain.repository.ServiceRepository;
import com.archscope.domain.model.servicediscovery.Service;
import com.archscope.domain.valueobject.PageResult;
import com.archscope.domain.valueobject.PageRequest;
import com.archscope.app.assembler.ServiceAssembler;
import com.archscope.facade.dto.PageResponseDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 服务发现应用服务实现
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ServiceDiscoveryServiceImpl implements ServiceDiscoveryService {

    private final ServiceRepository serviceRepository;
    private final ServiceAssembler serviceAssembler;

    @Override
    public List<ServiceDTO> findAllActiveServices() {
        log.debug("查找所有活跃服务");
        List<Service> services = serviceRepository.findByStatus("ACTIVE");
        return services.stream()
                .map(serviceAssembler::toDTO)
                .collect(Collectors.toList());
    }

    @Override
    public PageResponseDTO<ServiceDTO> findServices(int page, int size) {
        return findServicesWithSort(page, size, "registeredAt", "DESC");
    }

    @Override
    public PageResponseDTO<ServiceDTO> findServicesWithSort(int page, int size, String sortBy, String sortDirection) {
        log.debug("分页查询服务: page={}, size={}, sortBy={}, sortDirection={}", page, size, sortBy, sortDirection);
        
        PageRequest.SortDirection direction = "ASC".equalsIgnoreCase(sortDirection) 
                ? PageRequest.SortDirection.ASC : PageRequest.SortDirection.DESC;
        PageRequest pageRequest = PageRequest.of(page, size, sortBy, direction);
        
        PageResult<Service> servicePage = serviceRepository.findAll(pageRequest);
        
        List<ServiceDTO> serviceDTOs = servicePage.getContent().stream()
                .map(serviceAssembler::toDTO)
                .collect(Collectors.toList());
        
        return PageResponseDTO.<ServiceDTO>builder()
                .content(serviceDTOs)
                .page(page)
                .size(size)
                .totalElements(servicePage.getTotalElements())
                .totalPages(servicePage.getTotalPages())
                .first(servicePage.isFirst())
                .last(servicePage.isLast())
                .build();
    }

    @Override
    public List<ServiceDTO> findServicesByName(String name) {
        log.debug("根据名称搜索服务: name={}", name);
        List<Service> services = serviceRepository.findByNameContainingIgnoreCase(name);
        return services.stream()
                .map(serviceAssembler::toDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<ServiceDTO> findServicesByType(String type) {
        log.debug("根据类型查找服务: type={}", type);
        List<Service> services = serviceRepository.findByType(type);
        return services.stream()
                .map(serviceAssembler::toDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<ServiceDTO> findServicesByStatus(String status) {
        log.debug("根据状态查找服务: status={}", status);
        List<Service> services = serviceRepository.findByStatus(status);
        return services.stream()
                .map(serviceAssembler::toDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<ServiceDTO> findServicesByTags(List<String> tags) {
        log.debug("根据标签查找服务: tags={}", tags);
        List<Service> services = serviceRepository.findByTagsIn(tags);
        return services.stream()
                .map(serviceAssembler::toDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<ServiceDTO> findServicesByCapability(String capabilityName) {
        log.debug("根据能力查找服务: capabilityName={}", capabilityName);
        List<Service> services = serviceRepository.findByCapabilityName(capabilityName);
        return services.stream()
                .map(serviceAssembler::toDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<ServiceDTO> findServicesByMavenCoordinates(String groupId, String artifactId) {
        log.debug("根据Maven坐标查找服务: groupId={}, artifactId={}", groupId, artifactId);
        List<Service> services = serviceRepository.findByGroupIdAndArtifactId(groupId, artifactId);
        return services.stream()
                .map(serviceAssembler::toDTO)
                .collect(Collectors.toList());
    }

    @Override
    public ServiceDTO findServiceByExactMavenCoordinates(String groupId, String artifactId, String version) {
        log.debug("根据精确Maven坐标查找服务: groupId={}, artifactId={}, version={}", groupId, artifactId, version);
        Service service = serviceRepository.findByGroupIdAndArtifactIdAndVersion(groupId, artifactId, version);
        return service != null ? serviceAssembler.toDTO(service) : null;
    }

    @Override
    public PageResponseDTO<ServiceDTO> findServicesByCriteria(String name, String type, String status, 
                                                            List<String> tags, int page, int size) {
        ServiceQueryRequest queryRequest = ServiceQueryRequest.builder()
                .name(name)
                .type(type)
                .status(status)
                .tags(tags)
                .page(page)
                .size(size)
                .sortBy("registeredAt")
                .sortDirection("DESC")
                .build();
        return findServicesByCriteria(queryRequest);
    }

    @Override
    public PageResponseDTO<ServiceDTO> findServicesByCriteria(ServiceQueryRequest queryRequest) {
        log.debug("复杂条件查询服务: {}", queryRequest);
        
        PageRequest.SortDirection direction = "ASC".equalsIgnoreCase(queryRequest.getSortDirection()) 
                ? PageRequest.SortDirection.ASC : PageRequest.SortDirection.DESC;
        PageRequest pageRequest = PageRequest.of(queryRequest.getPage(), queryRequest.getSize(), 
                queryRequest.getSortBy(), direction);
        
        PageResult<Service> servicePage = serviceRepository.findByCriteria(
                queryRequest.getName(),
                queryRequest.getType(),
                queryRequest.getStatus(),
                queryRequest.getTags(),
                queryRequest.getGroupId(),
                queryRequest.getArtifactId(),
                queryRequest.getCapabilityName(),
                pageRequest
        );
        
        List<ServiceDTO> serviceDTOs = servicePage.getContent().stream()
                .map(serviceAssembler::toDTO)
                .collect(Collectors.toList());
        
        return PageResponseDTO.<ServiceDTO>builder()
                .content(serviceDTOs)
                .page(queryRequest.getPage())
                .size(queryRequest.getSize())
                .totalElements(servicePage.getTotalElements())
                .totalPages(servicePage.getTotalPages())
                .first(servicePage.isFirst())
                .last(servicePage.isLast())
                .build();
    }

    @Override
    public ServiceDTO getServiceById(String serviceId) {
        log.debug("根据ID获取服务: serviceId={}", serviceId);
        Service service = serviceRepository.findById(serviceId);
        return service != null ? serviceAssembler.toDTO(service) : null;
    }

    @Override
    public Object getServiceStatistics() {
        log.debug("获取服务统计信息");
        
        Map<String, Object> statistics = new HashMap<>();
        
        // 总服务数
        long totalServices = serviceRepository.count();
        statistics.put("totalServices", totalServices);
        
        // 活跃服务数
        long activeServices = serviceRepository.countByStatus("ACTIVE");
        statistics.put("activeServices", activeServices);
        
        // 按状态统计
        Map<String, Long> statusStats = serviceRepository.countByStatusGrouped();
        statistics.put("statusStatistics", statusStats);
        
        // 按类型统计
        Map<String, Long> typeStats = serviceRepository.countByTypeGrouped();
        statistics.put("typeStatistics", typeStats);
        
        // 最近注册的服务数（最近7天）
        long recentServices = serviceRepository.countRecentServices(7);
        statistics.put("recentServices", recentServices);
        
        return statistics;
    }
}