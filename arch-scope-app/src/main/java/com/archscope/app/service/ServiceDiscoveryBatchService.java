package com.archscope.app.service;

import com.archscope.app.dto.servicediscovery.CapabilityDTO;
import com.archscope.app.dto.servicediscovery.ServiceDTO;
import com.archscope.domain.model.servicediscovery.Capability;
import com.archscope.domain.model.servicediscovery.Service;
import com.archscope.domain.repository.CapabilityRepository;
import com.archscope.domain.repository.ServiceRepository;
import com.archscope.domain.valueobject.CapabilityId;
import com.archscope.domain.valueobject.ServiceId;
import com.archscope.infrastructure.repository.CapabilityRepositoryImpl;
import com.archscope.infrastructure.repository.ServiceRepositoryImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 服务发现批量操作服务
 */
@Service
public class ServiceDiscoveryBatchService {

    private final ServiceRepository serviceRepository;
    private final CapabilityRepository capabilityRepository;
    private final ServiceRegistryService serviceRegistryService;
    private final CapabilityManagementService capabilityManagementService;

    public ServiceDiscoveryBatchService(ServiceRepository serviceRepository,
                                       CapabilityRepository capabilityRepository,
                                       ServiceRegistryService serviceRegistryService,
                                       CapabilityManagementService capabilityManagementService) {
        this.serviceRepository = serviceRepository;
        this.capabilityRepository = capabilityRepository;
        this.serviceRegistryService = serviceRegistryService;
        this.capabilityManagementService = capabilityManagementService;
    }

    /**
     * 批量注册服务
     */
    @Transactional
    public List<ServiceDTO> batchRegisterServices(List<ServiceDTO> serviceDTOs) {
        // 转换为领域对象
        List<Service> services = serviceDTOs.stream()
                .map(serviceRegistryService::convertToService)
                .collect(Collectors.toList());

        // 批量保存
        if (serviceRepository instanceof ServiceRepositoryImpl) {
            services = ((ServiceRepositoryImpl) serviceRepository).saveBatch(services);
        } else {
            // 如果不是优化的实现，则逐个保存
            services = services.stream()
                    .map(serviceRepository::save)
                    .collect(Collectors.toList());
        }

        // 转换回DTO
        return services.stream()
                .map(serviceRegistryService::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 批量更新服务
     */
    @Transactional
    public List<ServiceDTO> batchUpdateServices(List<ServiceDTO> serviceDTOs) {
        // 验证所有服务都存在
        for (ServiceDTO serviceDTO : serviceDTOs) {
            Service existingService = serviceRepository.findById(ServiceId.of(serviceDTO.getServiceId()));
            if (existingService == null) {
                throw new IllegalArgumentException("Service not found: " + serviceDTO.getServiceId());
            }
        }

        return batchRegisterServices(serviceDTOs);
    }

    /**
     * 批量删除服务
     */
    @Transactional
    public boolean batchDeleteServices(List<String> serviceIds) {
        List<ServiceId> serviceIdObjects = serviceIds.stream()
                .map(ServiceId::of)
                .collect(Collectors.toList());

        // 先删除相关的能力
        for (ServiceId serviceId : serviceIdObjects) {
            capabilityRepository.deleteByServiceId(serviceId);
        }

        // 批量删除服务
        if (serviceRepository instanceof ServiceRepositoryImpl) {
            return ((ServiceRepositoryImpl) serviceRepository).deleteBatch(serviceIdObjects);
        } else {
            // 如果不是优化的实现，则逐个删除
            boolean allDeleted = true;
            for (ServiceId serviceId : serviceIdObjects) {
                if (!serviceRepository.delete(serviceId)) {
                    allDeleted = false;
                }
            }
            return allDeleted;
        }
    }

    /**
     * 批量注册能力
     */
    @Transactional
    public List<CapabilityDTO> batchRegisterCapabilities(List<CapabilityDTO> capabilityDTOs) {
        // 验证所有关联的服务都存在
        for (CapabilityDTO capabilityDTO : capabilityDTOs) {
            Service service = serviceRepository.findById(ServiceId.of(capabilityDTO.getServiceId()));
            if (service == null) {
                throw new IllegalArgumentException("Service not found: " + capabilityDTO.getServiceId());
            }
        }

        // 转换为领域对象
        List<Capability> capabilities = capabilityDTOs.stream()
                .map(capabilityManagementService::convertToCapability)
                .collect(Collectors.toList());

        // 批量保存
        if (capabilityRepository instanceof CapabilityRepositoryImpl) {
            capabilities = ((CapabilityRepositoryImpl) capabilityRepository).saveBatch(capabilities);
        } else {
            // 如果不是优化的实现，则逐个保存
            capabilities = capabilities.stream()
                    .map(capabilityRepository::save)
                    .collect(Collectors.toList());
        }

        // 转换回DTO
        return capabilities.stream()
                .map(capabilityManagementService::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 批量更新能力
     */
    @Transactional
    public List<CapabilityDTO> batchUpdateCapabilities(List<CapabilityDTO> capabilityDTOs) {
        // 验证所有能力都存在
        for (CapabilityDTO capabilityDTO : capabilityDTOs) {
            Capability existingCapability = capabilityRepository.findById(CapabilityId.of(capabilityDTO.getCapabilityId()));
            if (existingCapability == null) {
                throw new IllegalArgumentException("Capability not found: " + capabilityDTO.getCapabilityId());
            }
        }

        return batchRegisterCapabilities(capabilityDTOs);
    }

    /**
     * 批量删除能力
     */
    @Transactional
    public boolean batchDeleteCapabilities(List<String> capabilityIds) {
        List<CapabilityId> capabilityIdObjects = capabilityIds.stream()
                .map(CapabilityId::of)
                .collect(Collectors.toList());

        // 批量删除能力
        if (capabilityRepository instanceof CapabilityRepositoryImpl) {
            return ((CapabilityRepositoryImpl) capabilityRepository).deleteBatch(capabilityIdObjects);
        } else {
            // 如果不是优化的实现，则逐个删除
            boolean allDeleted = true;
            for (CapabilityId capabilityId : capabilityIdObjects) {
                if (!capabilityRepository.delete(capabilityId)) {
                    allDeleted = false;
                }
            }
            return allDeleted;
        }
    }

    /**
     * 批量查询服务（通过ID列表）
     */
    public List<ServiceDTO> batchGetServices(List<String> serviceIds) {
        List<Service> services = serviceIds.stream()
                .map(ServiceId::of)
                .map(serviceRepository::findById)
                .filter(service -> service != null)
                .collect(Collectors.toList());

        return services.stream()
                .map(serviceRegistryService::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 批量查询能力（通过ID列表）
     */
    public List<CapabilityDTO> batchGetCapabilities(List<String> capabilityIds) {
        List<Capability> capabilities = capabilityIds.stream()
                .map(CapabilityId::of)
                .map(capabilityRepository::findById)
                .filter(capability -> capability != null)
                .collect(Collectors.toList());

        return capabilities.stream()
                .map(capabilityManagementService::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 批量查询能力（通过服务ID列表）
     */
    public List<CapabilityDTO> batchGetCapabilitiesByServiceIds(List<String> serviceIds) {
        List<ServiceId> serviceIdObjects = serviceIds.stream()
                .map(ServiceId::of)
                .collect(Collectors.toList());

        List<Capability> capabilities;
        if (capabilityRepository instanceof CapabilityRepositoryImpl) {
            capabilities = ((CapabilityRepositoryImpl) capabilityRepository).findByServiceIds(serviceIdObjects);
        } else {
            // 如果不是优化的实现，则逐个查询
            capabilities = serviceIdObjects.stream()
                    .flatMap(serviceId -> capabilityRepository.findByServiceId(serviceId).stream())
                    .collect(Collectors.toList());
        }

        return capabilities.stream()
                .map(capabilityManagementService::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 批量导入服务和能力
     */
    @Transactional
    public void batchImportServicesWithCapabilities(List<ServiceDTO> serviceDTOs) {
        for (ServiceDTO serviceDTO : serviceDTOs) {
            // 注册服务
            ServiceDTO registeredService = serviceRegistryService.registerService(serviceDTO);
            
            // 注册服务的能力
            if (serviceDTO.getCapabilities() != null && !serviceDTO.getCapabilities().isEmpty()) {
                List<CapabilityDTO> capabilities = serviceDTO.getCapabilities().stream()
                        .peek(capability -> capability.setServiceId(registeredService.getServiceId()))
                        .collect(Collectors.toList());
                
                batchRegisterCapabilities(capabilities);
            }
        }
    }

    /**
     * 批量导出服务和能力
     */
    public List<ServiceDTO> batchExportServicesWithCapabilities(List<String> serviceIds) {
        List<ServiceDTO> services = batchGetServices(serviceIds);
        
        // 为每个服务添加其能力信息
        for (ServiceDTO service : services) {
            List<CapabilityDTO> capabilities = capabilityManagementService.getServiceCapabilities(service.getServiceId());
            service.setCapabilities(capabilities);
        }
        
        return services;
    }
}