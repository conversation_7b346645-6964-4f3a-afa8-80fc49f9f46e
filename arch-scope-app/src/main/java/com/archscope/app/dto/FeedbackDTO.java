package com.archscope.app.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.Instant;

/**
 * 反馈数据传输对象
 */
@Data
@Schema(description = "用户反馈信息", example = """
    {
        "id": "fb-001",
        "recommendationId": "rec-001",
        "requirementId": "req-001",
        "serviceId": "srv-001",
        "satisfied": true,
        "feedback": "推荐的服务很符合我的需求，API文档清晰，集成简单",
        "rating": 5,
        "createdAt": "2024-01-15T10:30:00Z"
    }
    """)
public class FeedbackDTO {

    /**
     * 反馈ID
     */
    @Schema(description = "反馈唯一标识符", example = "fb-001", required = true)
    private String id;

    /**
     * 推荐ID
     */
    @Schema(description = "关联的推荐ID", example = "rec-001", required = true)
    private String recommendationId;

    /**
     * 需求ID
     */
    @Schema(description = "关联的需求ID", example = "req-001")
    private String requirementId;

    /**
     * 服务ID
     */
    @Schema(description = "关联的服务ID", example = "srv-001", required = true)
    private String serviceId;

    /**
     * 是否满意
     */
    @Schema(description = "用户是否满意推荐结果", example = "true", required = true)
    private boolean satisfied;

    /**
     * 反馈内容
     */
    @Schema(description = "用户反馈内容", 
            example = "推荐的服务很符合我的需求，API文档清晰，集成简单")
    private String feedback;

    /**
     * 评分 (1-5)
     */
    @Schema(description = "用户评分，范围1-5", example = "5", minimum = "1", maximum = "5")
    private Integer rating;

    /**
     * 反馈时间
     */
    @Schema(description = "反馈创建时间", example = "2024-01-15T10:30:00Z", format = "date-time")
    private Instant createdAt;
}