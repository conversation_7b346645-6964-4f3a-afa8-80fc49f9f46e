package com.archscope.app.dto;

import lombok.Data;

import java.time.Instant;

/**
 * 反馈数据传输对象
 */
@Data
public class FeedbackDTO {

    /**
     * 反馈ID
     */
    private String id;

    /**
     * 推荐ID
     */
    private String recommendationId;

    /**
     * 需求ID
     */
    private String requirementId;

    /**
     * 服务ID
     */
    private String serviceId;

    /**
     * 是否满意
     */
    private boolean satisfied;

    /**
     * 反馈内容
     */
    private String feedback;

    /**
     * 评分 (1-5)
     */
    private Integer rating;

    /**
     * 反馈时间
     */
    private Instant createdAt;
}