package com.archscope.app.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 能力示例数据传输对象
 */
@Data
@Schema(description = "能力使用示例", example = """
    {
        "name": "用户名密码登录",
        "description": "使用用户名和密码进行登录认证",
        "requestExample": "{\\"username\\": \\"john\\", \\"password\\": \\"secret\\"}",
        "responseExample": "{\\"token\\": \\"jwt-token\\", \\"userId\\": \\"123\\", \\"expiresIn\\": 3600}"
    }
    """)
public class CapabilityExampleDTO {

    /**
     * 示例名称
     */
    @Schema(description = "示例名称", example = "用户名密码登录", required = true)
    private String name;

    /**
     * 示例描述
     */
    @Schema(description = "示例详细描述", example = "使用用户名和密码进行登录认证")
    private String description;

    /**
     * 请求示例
     */
    @Schema(description = "请求示例（JSON格式）", 
            example = "{\"username\": \"john\", \"password\": \"secret\"}")
    private String requestExample;

    /**
     * 响应示例
     */
    @Schema(description = "响应示例（JSON格式）", 
            example = "{\"token\": \"jwt-token\", \"userId\": \"123\", \"expiresIn\": 3600}")
    private String responseExample;
}