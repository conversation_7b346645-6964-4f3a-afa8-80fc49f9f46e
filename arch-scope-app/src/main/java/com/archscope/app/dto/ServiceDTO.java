package com.archscope.app.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.Instant;
import java.util.List;
import java.util.Map;

/**
 * 服务数据传输对象
 */
@Data
@Schema(description = "服务信息", example = """
    {
        "id": "srv-001",
        "name": "用户管理服务",
        "description": "提供用户注册、登录、权限管理等功能的微服务",
        "type": "REST_API",
        "version": "1.2.0",
        "endpoint": "https://api.example.com/user-service",
        "groupId": "com.example",
        "artifactId": "user-service",
        "tags": ["authentication", "user-management", "microservice"],
        "status": "ACTIVE",
        "metadata": {
            "environment": "production",
            "region": "us-east-1"
        },
        "registeredAt": "2024-01-15T10:30:00Z",
        "lastUpdatedAt": "2024-01-20T14:45:00Z",
        "capabilities": []
    }
    """)
public class ServiceDTO {

    /**
     * 服务ID
     */
    @Schema(description = "服务唯一标识符", example = "srv-001", required = true)
    private String id;

    /**
     * 服务名称
     */
    @Schema(description = "服务名称", example = "用户管理服务", required = true)
    private String name;

    /**
     * 服务描述
     */
    @Schema(description = "服务详细描述", example = "提供用户注册、登录、权限管理等功能的微服务")
    private String description;

    /**
     * 服务类型
     */
    @Schema(description = "服务类型", example = "REST_API", 
            allowableValues = {"REST_API", "GRAPHQL_API", "GRPC_SERVICE", "MESSAGE_QUEUE", "DATABASE", "CACHE", "OTHER"})
    private String type;

    /**
     * 服务版本
     */
    @Schema(description = "服务版本号（语义化版本）", example = "1.2.0", pattern = "^\\d+\\.\\d+\\.\\d+(-[a-zA-Z0-9]+)?$")
    private String version;

    /**
     * 服务端点URL
     */
    @Schema(description = "服务访问端点URL", example = "https://api.example.com/user-service", format = "uri")
    private String endpoint;

    /**
     * Maven/Gradle坐标 - groupId
     */
    @Schema(description = "Maven/Gradle坐标的groupId", example = "com.example")
    private String groupId;

    /**
     * Maven/Gradle坐标 - artifactId
     */
    @Schema(description = "Maven/Gradle坐标的artifactId", example = "user-service")
    private String artifactId;

    /**
     * 服务标签
     */
    @Schema(description = "服务标签列表，用于分类和搜索", 
            example = "[\"authentication\", \"user-management\", \"microservice\"]")
    private List<String> tags;

    /**
     * 服务状态
     */
    @Schema(description = "服务当前状态", example = "ACTIVE", 
            allowableValues = {"ACTIVE", "INACTIVE", "DEPRECATED", "MAINTENANCE", "UNKNOWN"})
    private String status;

    /**
     * 服务元数据
     */
    @Schema(description = "服务元数据键值对", 
            example = "{\"environment\": \"production\", \"region\": \"us-east-1\"}")
    private Map<String, String> metadata;

    /**
     * 注册时间
     */
    @Schema(description = "服务注册时间", example = "2024-01-15T10:30:00Z", format = "date-time")
    private Instant registeredAt;

    /**
     * 最后更新时间
     */
    @Schema(description = "服务最后更新时间", example = "2024-01-20T14:45:00Z", format = "date-time")
    private Instant lastUpdatedAt;

    /**
     * 服务能力列表
     */
    @Schema(description = "服务提供的能力列表")
    private List<CapabilityDTO> capabilities;
}