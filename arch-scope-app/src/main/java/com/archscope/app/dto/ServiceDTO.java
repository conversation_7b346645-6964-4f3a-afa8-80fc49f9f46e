package com.archscope.app.dto;

import lombok.Data;

import java.time.Instant;
import java.util.List;
import java.util.Map;

/**
 * 服务数据传输对象
 */
@Data
public class ServiceDTO {

    /**
     * 服务ID
     */
    private String id;

    /**
     * 服务名称
     */
    private String name;

    /**
     * 服务描述
     */
    private String description;

    /**
     * 服务类型
     */
    private String type;

    /**
     * 服务版本
     */
    private String version;

    /**
     * 服务端点URL
     */
    private String endpoint;

    /**
     * Maven/Gradle坐标 - groupId
     */
    private String groupId;

    /**
     * Maven/Gradle坐标 - artifactId
     */
    private String artifactId;

    /**
     * 服务标签
     */
    private List<String> tags;

    /**
     * 服务状态
     */
    private String status;

    /**
     * 服务元数据
     */
    private Map<String, String> metadata;

    /**
     * 注册时间
     */
    private Instant registeredAt;

    /**
     * 最后更新时间
     */
    private Instant lastUpdatedAt;

    /**
     * 服务能力列表
     */
    private List<CapabilityDTO> capabilities;
}