package com.archscope.app.dto;

import lombok.Data;

import java.time.Instant;
import java.util.List;

/**
 * 能力数据传输对象
 */
@Data
public class CapabilityDTO {

    /**
     * 能力ID
     */
    private String id;

    /**
     * 关联的服务ID
     */
    private String serviceId;

    /**
     * 能力名称
     */
    private String name;

    /**
     * 能力描述
     */
    private String description;

    /**
     * 能力版本
     */
    private String version;

    /**
     * 能力示例
     */
    private List<CapabilityExampleDTO> examples;

    /**
     * 能力标签
     */
    private List<String> tags;

    /**
     * 是否已废弃
     */
    private boolean deprecated;

    /**
     * 创建时间
     */
    private Instant createdAt;

    /**
     * 最后更新时间
     */
    private Instant lastUpdatedAt;
}