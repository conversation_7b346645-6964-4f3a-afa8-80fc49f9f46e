package com.archscope.app.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.Instant;
import java.util.List;

/**
 * 能力数据传输对象
 */
@Data
@Schema(description = "服务能力信息", example = """
    {
        "id": "cap-001",
        "serviceId": "srv-001",
        "name": "用户认证",
        "description": "提供用户登录认证功能，支持用户名密码和第三方OAuth认证",
        "version": "1.0.0",
        "examples": [
            {
                "name": "用户名密码登录",
                "description": "使用用户名和密码进行登录认证",
                "requestExample": "{\\"username\\": \\"john\\", \\"password\\": \\"secret\\"}",
                "responseExample": "{\\"token\\": \\"jwt-token\\", \\"userId\\": \\"123\\"}"
            }
        ],
        "tags": ["authentication", "security"],
        "deprecated": false,
        "createdAt": "2024-01-15T10:30:00Z",
        "lastUpdatedAt": "2024-01-20T14:45:00Z"
    }
    """)
public class CapabilityDTO {

    /**
     * 能力ID
     */
    @Schema(description = "能力唯一标识符", example = "cap-001", required = true)
    private String id;

    /**
     * 关联的服务ID
     */
    @Schema(description = "提供此能力的服务ID", example = "srv-001", required = true)
    private String serviceId;

    /**
     * 能力名称
     */
    @Schema(description = "能力名称", example = "用户认证", required = true)
    private String name;

    /**
     * 能力描述
     */
    @Schema(description = "能力详细描述", example = "提供用户登录认证功能，支持用户名密码和第三方OAuth认证")
    private String description;

    /**
     * 能力版本
     */
    @Schema(description = "能力版本号（语义化版本）", example = "1.0.0", pattern = "^\\d+\\.\\d+\\.\\d+(-[a-zA-Z0-9]+)?$")
    private String version;

    /**
     * 能力示例
     */
    @Schema(description = "能力使用示例列表")
    private List<CapabilityExampleDTO> examples;

    /**
     * 能力标签
     */
    @Schema(description = "能力标签列表，用于分类和搜索", 
            example = "[\"authentication\", \"security\"]")
    private List<String> tags;

    /**
     * 是否已废弃
     */
    @Schema(description = "能力是否已废弃", example = "false")
    private boolean deprecated;

    /**
     * 创建时间
     */
    @Schema(description = "能力创建时间", example = "2024-01-15T10:30:00Z", format = "date-time")
    private Instant createdAt;

    /**
     * 最后更新时间
     */
    @Schema(description = "能力最后更新时间", example = "2024-01-20T14:45:00Z", format = "date-time")
    private Instant lastUpdatedAt;
}