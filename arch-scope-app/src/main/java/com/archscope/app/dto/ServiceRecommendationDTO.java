package com.archscope.app.dto;

import lombok.Data;

/**
 * 服务推荐数据传输对象
 */
@Data
public class ServiceRecommendationDTO {

    /**
     * 推荐ID
     */
    private String recommendationId;

    /**
     * 推荐的服务
     */
    private ServiceDTO service;

    /**
     * 匹配度分数 (0-100)
     */
    private int matchingScore;

    /**
     * 推荐分数 (0-100)
     */
    private int recommendationScore;

    /**
     * 推荐原因
     */
    private String reason;

    /**
     * 匹配的能力列表
     */
    private java.util.List<String> matchedCapabilities;
}