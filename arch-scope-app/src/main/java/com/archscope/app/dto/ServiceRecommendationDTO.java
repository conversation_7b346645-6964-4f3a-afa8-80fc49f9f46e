package com.archscope.app.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 服务推荐数据传输对象
 */
@Data
@Schema(description = "服务推荐信息", example = """
    {
        "recommendationId": "rec-001",
        "service": {
            "id": "srv-001",
            "name": "用户管理服务",
            "description": "提供用户注册、登录、权限管理等功能的微服务",
            "type": "REST_API",
            "version": "1.2.0",
            "endpoint": "https://api.example.com/user-service",
            "status": "ACTIVE"
        },
        "matchingScore": 85,
        "recommendationScore": 92,
        "reason": "该服务提供了您需要的用户认证、用户注册和权限管理能力，匹配度很高",
        "matchedCapabilities": ["用户认证", "用户注册", "权限管理"]
    }
    """)
public class ServiceRecommendationDTO {

    /**
     * 推荐ID
     */
    @Schema(description = "推荐唯一标识符", example = "rec-001", required = true)
    private String recommendationId;

    /**
     * 推荐的服务
     */
    @Schema(description = "推荐的服务详细信息", required = true)
    private ServiceDTO service;

    /**
     * 匹配度分数 (0-100)
     */
    @Schema(description = "需求匹配度分数，范围0-100", example = "85", minimum = "0", maximum = "100")
    private int matchingScore;

    /**
     * 推荐分数 (0-100)
     */
    @Schema(description = "综合推荐分数，范围0-100", example = "92", minimum = "0", maximum = "100")
    private int recommendationScore;

    /**
     * 推荐原因
     */
    @Schema(description = "推荐原因说明", 
            example = "该服务提供了您需要的用户认证、用户注册和权限管理能力，匹配度很高")
    private String reason;

    /**
     * 匹配的能力列表
     */
    @Schema(description = "与需求匹配的能力列表", 
            example = "[\"用户认证\", \"用户注册\", \"权限管理\"]")
    private java.util.List<String> matchedCapabilities;
}