package com.archscope.app.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.validation.constraints.Min;
import java.util.List;

/**
 * 服务查询请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ServiceQueryRequest {

    /**
     * 服务名称（支持模糊匹配）
     */
    private String name;

    /**
     * 服务类型
     */
    private String type;

    /**
     * 服务状态
     */
    private String status;

    /**
     * 标签列表
     */
    private List<String> tags;

    /**
     * 能力名称
     */
    private String capabilityName;

    /**
     * Maven坐标 - groupId
     */
    private String groupId;

    /**
     * Maven坐标 - artifactId
     */
    private String artifactId;

    /**
     * Maven坐标 - version（用于精确匹配）
     */
    private String version;

    /**
     * 页码（从0开始）
     */
    @Min(value = 0, message = "页码不能小于0")
    private Integer page = 0;

    /**
     * 每页大小
     */
    @Min(value = 1, message = "每页大小不能小于1")
    private Integer size = 10;

    /**
     * 排序字段
     */
    private String sortBy = "registeredAt";

    /**
     * 排序方向（ASC/DESC）
     */
    private String sortDirection = "DESC";
}