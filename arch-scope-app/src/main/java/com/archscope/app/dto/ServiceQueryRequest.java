package com.archscope.app.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.validation.constraints.Min;
import javax.validation.constraints.Max;
import java.util.List;

/**
 * 服务查询请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "服务查询请求参数", example = """
    {
        "name": "用户",
        "type": "REST_API",
        "status": "ACTIVE",
        "tags": ["authentication", "microservice"],
        "capabilityName": "用户认证",
        "groupId": "com.example",
        "artifactId": "user-service",
        "page": 0,
        "size": 10,
        "sortBy": "registeredAt",
        "sortDirection": "DESC"
    }
    """)
public class ServiceQueryRequest {

    /**
     * 服务名称（支持模糊匹配）
     */
    @Schema(description = "服务名称，支持模糊匹配", example = "用户")
    private String name;

    /**
     * 服务类型
     */
    @Schema(description = "服务类型", example = "REST_API", 
            allowableValues = {"REST_API", "GRAPHQL_API", "GRPC_SERVICE", "MESSAGE_QUEUE", "DATABASE", "CACHE", "OTHER"})
    private String type;

    /**
     * 服务状态
     */
    @Schema(description = "服务状态", example = "ACTIVE", 
            allowableValues = {"ACTIVE", "INACTIVE", "DEPRECATED", "MAINTENANCE", "UNKNOWN"})
    private String status;

    /**
     * 标签列表
     */
    @Schema(description = "服务标签列表", example = "[\"authentication\", \"microservice\"]")
    private List<String> tags;

    /**
     * 能力名称
     */
    @Schema(description = "服务提供的能力名称", example = "用户认证")
    private String capabilityName;

    /**
     * Maven坐标 - groupId
     */
    @Schema(description = "Maven/Gradle坐标的groupId", example = "com.example")
    private String groupId;

    /**
     * Maven坐标 - artifactId
     */
    @Schema(description = "Maven/Gradle坐标的artifactId", example = "user-service")
    private String artifactId;

    /**
     * Maven坐标 - version（用于精确匹配）
     */
    @Schema(description = "Maven/Gradle坐标的version，用于精确匹配", example = "1.2.0")
    private String version;

    /**
     * 页码（从0开始）
     */
    @Schema(description = "页码，从0开始", example = "0", minimum = "0")
    @Min(value = 0, message = "页码不能小于0")
    private Integer page = 0;

    /**
     * 每页大小
     */
    @Schema(description = "每页大小，最大100", example = "10", minimum = "1", maximum = "100")
    @Min(value = 1, message = "每页大小不能小于1")
    @Max(value = 100, message = "每页大小不能超过100")
    private Integer size = 10;

    /**
     * 排序字段
     */
    @Schema(description = "排序字段", example = "registeredAt", 
            allowableValues = {"name", "type", "version", "registeredAt", "lastUpdatedAt", "status"})
    private String sortBy = "registeredAt";

    /**
     * 排序方向（ASC/DESC）
     */
    @Schema(description = "排序方向", example = "DESC", allowableValues = {"ASC", "DESC"})
    private String sortDirection = "DESC";
}