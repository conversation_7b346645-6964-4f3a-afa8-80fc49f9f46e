package com.archscope.app.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.Instant;
import java.util.List;

/**
 * 需求数据传输对象
 */
@Data
@Schema(description = "用户需求信息", example = """
    {
        "id": "req-001",
        "description": "需要一个完整的用户管理系统，包括用户注册、登录、权限管理和个人资料管理功能",
        "requiredCapabilities": ["用户认证", "用户注册", "权限管理", "个人资料管理"],
        "priority": "HIGH",
        "createdAt": "2024-01-15T10:30:00Z",
        "lastUpdatedAt": "2024-01-15T10:30:00Z"
    }
    """)
public class RequirementDTO {

    /**
     * 需求ID
     */
    @Schema(description = "需求唯一标识符", example = "req-001", required = true)
    private String id;

    /**
     * 需求描述
     */
    @Schema(description = "需求详细描述", 
            example = "需要一个完整的用户管理系统，包括用户注册、登录、权限管理和个人资料管理功能", 
            required = true)
    private String description;

    /**
     * 所需能力列表
     */
    @Schema(description = "需求所需的能力列表", 
            example = "[\"用户认证\", \"用户注册\", \"权限管理\", \"个人资料管理\"]")
    private List<String> requiredCapabilities;

    /**
     * 需求优先级
     */
    @Schema(description = "需求优先级", example = "HIGH", 
            allowableValues = {"LOW", "MEDIUM", "HIGH", "CRITICAL"})
    private String priority;

    /**
     * 创建时间
     */
    @Schema(description = "需求创建时间", example = "2024-01-15T10:30:00Z", format = "date-time")
    private Instant createdAt;

    /**
     * 最后更新时间
     */
    @Schema(description = "需求最后更新时间", example = "2024-01-15T10:30:00Z", format = "date-time")
    private Instant lastUpdatedAt;
}