package com.archscope.app.dto;

import lombok.Data;

import java.time.Instant;
import java.util.List;

/**
 * 需求数据传输对象
 */
@Data
public class RequirementDTO {

    /**
     * 需求ID
     */
    private String id;

    /**
     * 需求描述
     */
    private String description;

    /**
     * 所需能力列表
     */
    private List<String> requiredCapabilities;

    /**
     * 需求优先级
     */
    private String priority;

    /**
     * 创建时间
     */
    private Instant createdAt;

    /**
     * 最后更新时间
     */
    private Instant lastUpdatedAt;
}