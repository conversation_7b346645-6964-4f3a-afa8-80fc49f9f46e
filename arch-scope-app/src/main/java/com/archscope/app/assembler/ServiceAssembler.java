package com.archscope.app.assembler;

import com.archscope.app.dto.ServiceDTO;
import com.archscope.app.dto.CapabilityDTO;
import com.archscope.domain.model.servicediscovery.Service;
import com.archscope.domain.valueobject.Capability;
import com.archscope.domain.valueobject.Tag;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 服务实体与DTO之间的转换器
 */
@Component
public class ServiceAssembler {

    /**
     * 将领域模型Service转换为ServiceDTO
     *
     * @param service 领域模型Service
     * @return ServiceDTO
     */
    public ServiceDTO toDTO(Service service) {
        if (service == null) {
            return null;
        }

        ServiceDTO dto = new ServiceDTO();
        dto.setId(service.getId().getValue());
        dto.setName(service.getName());
        dto.setDescription(service.getDescription());
        dto.setType(service.getType().name());
        dto.setVersion(service.getVersion().getValue());
        dto.setEndpoint(service.getEndpoint().toString());
        dto.setGroupId(service.getGroupId());
        dto.setArtifactId(service.getArtifactId());
        dto.setStatus(service.getStatus().name());
        dto.setRegisteredAt(service.getRegisteredAt());
        dto.setLastUpdatedAt(service.getLastUpdatedAt());

        // 转换标签
        List<String> tags = service.getTags().stream()
                .map(Tag::getValue)
                .collect(Collectors.toList());
        dto.setTags(tags);

        // 转换能力 - 暂时设为空列表
        dto.setCapabilities(List.of());

        // 转换元数据 - 暂时设为空Map
        dto.setMetadata(new HashMap<>());

        return dto;
    }


}