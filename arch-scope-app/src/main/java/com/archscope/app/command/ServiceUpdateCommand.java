package com.archscope.app.command;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 服务更新命令
 */
@Data
public class ServiceUpdateCommand {

    /**
     * 服务名称
     */
    private String name;

    /**
     * 服务描述
     */
    private String description;

    /**
     * 服务类型
     */
    private String type;

    /**
     * 服务版本
     */
    private String version;

    /**
     * 服务端点URL
     */
    private String endpoint;

    /**
     * Maven/Gradle坐标 - groupId
     */
    private String groupId;

    /**
     * Maven/Gradle坐标 - artifactId
     */
    private String artifactId;

    /**
     * 服务标签
     */
    private List<String> tags;

    /**
     * 服务状态
     */
    private String status;

    /**
     * 服务元数据
     */
    private Map<String, String> metadata;
}