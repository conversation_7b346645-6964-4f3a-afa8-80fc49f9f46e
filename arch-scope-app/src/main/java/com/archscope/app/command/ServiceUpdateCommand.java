package com.archscope.app.command;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.List;
import java.util.Map;

/**
 * 服务更新命令
 */
@Data
@Schema(description = "服务更新请求", example = """
    {
        "name": "用户管理服务 v2",
        "description": "提供用户注册、登录、权限管理、个人资料管理等功能的微服务",
        "type": "REST_API",
        "version": "2.0.0",
        "endpoint": "https://api.example.com/user-service/v2",
        "groupId": "com.example",
        "artifactId": "user-service",
        "tags": ["authentication", "user-management", "microservice", "v2"],
        "status": "ACTIVE",
        "metadata": {
            "environment": "production",
            "region": "us-east-1",
            "maintainer": "<EMAIL>",
            "lastDeployment": "2024-01-20T10:00:00Z"
        }
    }
    """)
public class ServiceUpdateCommand {

    /**
     * 服务名称
     */
    @Schema(description = "服务名称", example = "用户管理服务 v2")
    @Size(min = 2, max = 100, message = "服务名称长度必须在2-100字符之间")
    private String name;

    /**
     * 服务描述
     */
    @Schema(description = "服务详细描述", example = "提供用户注册、登录、权限管理、个人资料管理等功能的微服务")
    @Size(max = 500, message = "服务描述长度不能超过500字符")
    private String description;

    /**
     * 服务类型
     */
    @Schema(description = "服务类型", example = "REST_API",
            allowableValues = {"REST_API", "GRAPHQL_API", "GRPC_SERVICE", "MESSAGE_QUEUE", "DATABASE", "CACHE", "OTHER"})
    private String type;

    /**
     * 服务版本
     */
    @Schema(description = "服务版本号（语义化版本）", example = "2.0.0")
    @Pattern(regexp = "^\\d+\\.\\d+\\.\\d+(-[a-zA-Z0-9]+)?$", message = "版本号格式不正确，应使用语义化版本格式")
    private String version;

    /**
     * 服务端点URL
     */
    @Schema(description = "服务访问端点URL", example = "https://api.example.com/user-service/v2", format = "uri")
    private String endpoint;

    /**
     * Maven/Gradle坐标 - groupId
     */
    @Schema(description = "Maven/Gradle坐标的groupId", example = "com.example")
    @Pattern(regexp = "^[a-zA-Z][a-zA-Z0-9_]*(?:\\.[a-zA-Z][a-zA-Z0-9_]*)*$", 
             message = "groupId格式不正确")
    private String groupId;

    /**
     * Maven/Gradle坐标 - artifactId
     */
    @Schema(description = "Maven/Gradle坐标的artifactId", example = "user-service")
    @Pattern(regexp = "^[a-zA-Z][a-zA-Z0-9_-]*$", message = "artifactId格式不正确")
    private String artifactId;

    /**
     * 服务标签
     */
    @Schema(description = "服务标签列表，用于分类和搜索", 
            example = "[\"authentication\", \"user-management\", \"microservice\", \"v2\"]")
    @Size(max = 20, message = "标签数量不能超过20个")
    private List<@NotBlank(message = "标签不能为空") @Size(max = 50, message = "标签长度不能超过50字符") String> tags;

    /**
     * 服务状态
     */
    @Schema(description = "服务状态", example = "ACTIVE", 
            allowableValues = {"ACTIVE", "INACTIVE", "DEPRECATED", "MAINTENANCE", "UNKNOWN"})
    private String status;

    /**
     * 服务元数据
     */
    @Schema(description = "服务元数据键值对", 
            example = "{\"environment\": \"production\", \"region\": \"us-east-1\", \"maintainer\": \"<EMAIL>\", \"lastDeployment\": \"2024-01-20T10:00:00Z\"}")
    @Size(max = 50, message = "元数据项数量不能超过50个")
    private Map<@NotBlank(message = "元数据键不能为空") @Size(max = 100, message = "元数据键长度不能超过100字符") String, 
                @Size(max = 500, message = "元数据值长度不能超过500字符") String> metadata;
}