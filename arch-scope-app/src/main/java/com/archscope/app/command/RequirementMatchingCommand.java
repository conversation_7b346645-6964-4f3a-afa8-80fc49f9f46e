package com.archscope.app.command;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 需求匹配命令
 */
@Data
@Schema(description = "需求匹配请求", example = """
    {
        "description": "需要一个完整的用户管理系统，包括用户注册、登录、权限管理和个人资料管理功能",
        "requiredCapabilities": ["用户认证", "用户注册", "权限管理", "个人资料管理"],
        "priority": "HIGH",
        "maxResults": 10,
        "minMatchingScore": 60
    }
    """)
public class RequirementMatchingCommand {

    /**
     * 需求描述
     */
    @Schema(description = "需求详细描述", 
            example = "需要一个完整的用户管理系统，包括用户注册、登录、权限管理和个人资料管理功能", 
            required = true)
    @NotBlank(message = "需求描述不能为空")
    @Size(min = 10, max = 1000, message = "需求描述长度必须在10-1000字符之间")
    private String description;

    /**
     * 所需能力列表
     */
    @Schema(description = "需求所需的能力列表", 
            example = "[\"用户认证\", \"用户注册\", \"权限管理\", \"个人资料管理\"]")
    @Size(max = 50, message = "所需能力数量不能超过50个")
    private List<@NotBlank(message = "能力名称不能为空") @Size(max = 100, message = "能力名称长度不能超过100字符") String> requiredCapabilities;

    /**
     * 需求优先级
     */
    @Schema(description = "需求优先级", example = "HIGH", 
            allowableValues = {"LOW", "MEDIUM", "HIGH", "CRITICAL"})
    private String priority = "MEDIUM";

    /**
     * 最大返回结果数
     */
    @Schema(description = "最大返回结果数", example = "10", minimum = "1", maximum = "100")
    @Min(value = 1, message = "最大返回结果数不能小于1")
    @Max(value = 100, message = "最大返回结果数不能超过100")
    private Integer maxResults = 10;

    /**
     * 最小匹配度阈值 (0-100)
     */
    @Schema(description = "最小匹配度阈值，范围0-100", example = "60", minimum = "0", maximum = "100")
    @Min(value = 0, message = "最小匹配度阈值不能小于0")
    @Max(value = 100, message = "最小匹配度阈值不能超过100")
    private Integer minMatchingScore = 50;
}