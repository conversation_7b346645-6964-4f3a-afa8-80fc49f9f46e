package com.archscope.app.command;

import lombok.Data;

import java.util.List;

/**
 * 需求匹配命令
 */
@Data
public class RequirementMatchingCommand {

    /**
     * 需求描述
     */
    private String description;

    /**
     * 所需能力列表
     */
    private List<String> requiredCapabilities;

    /**
     * 需求优先级
     */
    private String priority;

    /**
     * 最大返回结果数
     */
    private Integer maxResults;

    /**
     * 最小匹配度阈值 (0-100)
     */
    private Integer minMatchingScore;
}