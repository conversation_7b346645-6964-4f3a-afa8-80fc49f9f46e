package com.archscope.app.command;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 推荐反馈命令
 */
@Data
@Schema(description = "推荐反馈请求", example = """
    {
        "recommendationId": "rec-001",
        "requirementId": "req-001",
        "serviceId": "srv-001",
        "satisfied": true,
        "feedback": "推荐的服务很符合我的需求，API文档清晰，集成简单，性能也很好",
        "rating": 5
    }
    """)
public class RecommendationFeedbackCommand {

    /**
     * 推荐ID
     */
    @Schema(description = "推荐唯一标识符", example = "rec-001", required = true)
    @NotBlank(message = "推荐ID不能为空")
    private String recommendationId;

    /**
     * 需求ID
     */
    @Schema(description = "关联的需求ID", example = "req-001")
    private String requirementId;

    /**
     * 服务ID
     */
    @Schema(description = "关联的服务ID", example = "srv-001", required = true)
    @NotBlank(message = "服务ID不能为空")
    private String serviceId;

    /**
     * 是否满意
     */
    @Schema(description = "用户是否满意推荐结果", example = "true", required = true)
    private boolean satisfied;

    /**
     * 反馈内容
     */
    @Schema(description = "用户反馈内容", 
            example = "推荐的服务很符合我的需求，API文档清晰，集成简单，性能也很好")
    @Size(max = 1000, message = "反馈内容长度不能超过1000字符")
    private String feedback;

    /**
     * 评分 (1-5)
     */
    @Schema(description = "用户评分，范围1-5", example = "5", minimum = "1", maximum = "5")
    @Min(value = 1, message = "评分不能小于1")
    @Max(value = 5, message = "评分不能超过5")
    private Integer rating;
}