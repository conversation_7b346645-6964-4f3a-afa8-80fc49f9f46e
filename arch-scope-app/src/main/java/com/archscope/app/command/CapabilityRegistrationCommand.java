package com.archscope.app.command;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 能力注册命令
 */
@Data
public class CapabilityRegistrationCommand {

    /**
     * 能力名称
     */
    @NotBlank(message = "{capability.name.notblank}")
    @Size(min = 2, max = 100, message = "{capability.name.size}")
    private String name;

    /**
     * 能力描述
     */
    @Size(max = 500, message = "{capability.description.size}")
    private String description;

    /**
     * 能力版本 (支持语义化版本格式)
     */
    @NotBlank(message = "{capability.version.notblank}")
    @Pattern(regexp = "^\\d+\\.\\d+\\.\\d+(-[a-zA-Z0-9]+)?$", 
             message = "{capability.version.pattern}")
    private String version;

    /**
     * 能力示例
     */
    @Valid
    @Size(max = 10, message = "{capability.examples.size}")
    private List<CapabilityExampleCommand> examples;

    /**
     * 能力标签
     */
    @Size(max = 20, message = "{capability.tags.size}")
    private List<@NotBlank(message = "{capability.tag.notblank}") String> tags;

    /**
     * 能力示例命令
     */
    @Data
    public static class CapabilityExampleCommand {
        /**
         * 示例名称
         */
        @NotBlank(message = "{capability.example.name.notblank}")
        @Size(min = 2, max = 100, message = "{capability.example.name.size}")
        private String name;

        /**
         * 示例描述
         */
        @Size(max = 300, message = "{capability.example.description.size}")
        private String description;

        /**
         * 请求示例
         */
        @Size(max = 2000, message = "{capability.example.request.size}")
        private String requestExample;

        /**
         * 响应示例
         */
        @Size(max = 2000, message = "{capability.example.response.size}")
        private String responseExample;
    }
}