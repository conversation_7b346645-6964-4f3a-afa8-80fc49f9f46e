package com.archscope.app.command;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 能力注册命令
 */
@Data
@Schema(description = "能力注册请求", example = """
    {
        "name": "用户认证",
        "description": "提供用户登录认证功能，支持用户名密码和第三方OAuth认证",
        "version": "1.0.0",
        "examples": [
            {
                "name": "用户名密码登录",
                "description": "使用用户名和密码进行登录认证",
                "requestExample": "{\\"username\\": \\"john\\", \\"password\\": \\"secret\\"}",
                "responseExample": "{\\"token\\": \\"jwt-token\\", \\"userId\\": \\"123\\", \\"expiresIn\\": 3600}"
            }
        ],
        "tags": ["authentication", "security", "login"]
    }
    """)
public class CapabilityRegistrationCommand {

    /**
     * 能力名称
     */
    @Schema(description = "能力名称", example = "用户认证", required = true)
    @NotBlank(message = "能力名称不能为空")
    @Size(min = 2, max = 100, message = "能力名称长度必须在2-100字符之间")
    private String name;

    /**
     * 能力描述
     */
    @Schema(description = "能力详细描述", example = "提供用户登录认证功能，支持用户名密码和第三方OAuth认证")
    @Size(max = 500, message = "能力描述长度不能超过500字符")
    private String description;

    /**
     * 能力版本 (支持语义化版本格式)
     */
    @Schema(description = "能力版本号（语义化版本）", example = "1.0.0", required = true)
    @NotBlank(message = "能力版本不能为空")
    @Pattern(regexp = "^\\d+\\.\\d+\\.\\d+(-[a-zA-Z0-9]+)?$", 
             message = "版本号格式不正确，应使用语义化版本格式")
    private String version;

    /**
     * 能力示例
     */
    @Schema(description = "能力使用示例列表")
    @Valid
    @Size(max = 10, message = "能力示例数量不能超过10个")
    private List<CapabilityExampleCommand> examples;

    /**
     * 能力标签
     */
    @Schema(description = "能力标签列表，用于分类和搜索", 
            example = "[\"authentication\", \"security\", \"login\"]")
    @Size(max = 20, message = "能力标签数量不能超过20个")
    private List<@NotBlank(message = "标签不能为空") @Size(max = 50, message = "标签长度不能超过50字符") String> tags;

    /**
     * 能力示例命令
     */
    @Data
    @Schema(description = "能力使用示例", example = """
        {
            "name": "用户名密码登录",
            "description": "使用用户名和密码进行登录认证",
            "requestExample": "{\\"username\\": \\"john\\", \\"password\\": \\"secret\\"}",
            "responseExample": "{\\"token\\": \\"jwt-token\\", \\"userId\\": \\"123\\", \\"expiresIn\\": 3600}"
        }
        """)
    public static class CapabilityExampleCommand {
        /**
         * 示例名称
         */
        @Schema(description = "示例名称", example = "用户名密码登录", required = true)
        @NotBlank(message = "示例名称不能为空")
        @Size(min = 2, max = 100, message = "示例名称长度必须在2-100字符之间")
        private String name;

        /**
         * 示例描述
         */
        @Schema(description = "示例详细描述", example = "使用用户名和密码进行登录认证")
        @Size(max = 300, message = "示例描述长度不能超过300字符")
        private String description;

        /**
         * 请求示例
         */
        @Schema(description = "请求示例（JSON格式）", 
                example = "{\"username\": \"john\", \"password\": \"secret\"}")
        @Size(max = 2000, message = "请求示例长度不能超过2000字符")
        private String requestExample;

        /**
         * 响应示例
         */
        @Schema(description = "响应示例（JSON格式）", 
                example = "{\"token\": \"jwt-token\", \"userId\": \"123\", \"expiresIn\": 3600}")
        @Size(max = 2000, message = "响应示例长度不能超过2000字符")
        private String responseExample;
    }
}