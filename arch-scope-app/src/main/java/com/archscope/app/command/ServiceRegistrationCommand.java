package com.archscope.app.command;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * 服务注册命令
 */
@Data
public class ServiceRegistrationCommand {

    /**
     * 服务名称
     */
    @NotBlank(message = "Service name cannot be blank")
    private String name;

    /**
     * 服务描述
     */
    private String description;

    /**
     * 服务类型
     */
    @NotBlank(message = "Service type cannot be blank")
    private String type;

    /**
     * 服务版本
     */
    @NotBlank(message = "Service version cannot be blank")
    private String version;

    /**
     * 服务端点URL
     */
    @NotBlank(message = "Service endpoint cannot be blank")
    private String endpoint;

    /**
     * Maven/Gradle坐标 - groupId
     */
    private String groupId;

    /**
     * Maven/Gradle坐标 - artifactId
     */
    private String artifactId;

    /**
     * 服务标签
     */
    private List<String> tags;

    /**
     * 服务状态
     */
    private String status;

    /**
     * 服务元数据
     */
    private Map<String, String> metadata;
}