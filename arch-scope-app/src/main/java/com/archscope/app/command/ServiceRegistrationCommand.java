package com.archscope.app.command;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.List;
import java.util.Map;

/**
 * 服务注册命令
 */
@Data
@Schema(description = "服务注册请求", example = """
    {
        "name": "用户管理服务",
        "description": "提供用户注册、登录、权限管理等功能的微服务",
        "type": "REST_API",
        "version": "1.0.0",
        "endpoint": "https://api.example.com/user-service",
        "groupId": "com.example",
        "artifactId": "user-service",
        "tags": ["authentication", "user-management", "microservice"],
        "status": "ACTIVE",
        "metadata": {
            "environment": "production",
            "region": "us-east-1",
            "maintainer": "<EMAIL>"
        }
    }
    """)
public class ServiceRegistrationCommand {

    /**
     * 服务名称
     */
    @Schema(description = "服务名称", example = "用户管理服务", required = true)
    @NotBlank(message = "服务名称不能为空")
    @Size(min = 2, max = 100, message = "服务名称长度必须在2-100字符之间")
    private String name;

    /**
     * 服务描述
     */
    @Schema(description = "服务详细描述", example = "提供用户注册、登录、权限管理等功能的微服务")
    @Size(max = 500, message = "服务描述长度不能超过500字符")
    private String description;

    /**
     * 服务类型
     */
    @Schema(description = "服务类型", example = "REST_API", required = true,
            allowableValues = {"REST_API", "GRAPHQL_API", "GRPC_SERVICE", "MESSAGE_QUEUE", "DATABASE", "CACHE", "OTHER"})
    @NotBlank(message = "服务类型不能为空")
    private String type;

    /**
     * 服务版本
     */
    @Schema(description = "服务版本号（语义化版本）", example = "1.0.0", required = true)
    @NotBlank(message = "服务版本不能为空")
    @Pattern(regexp = "^\\d+\\.\\d+\\.\\d+(-[a-zA-Z0-9]+)?$", message = "版本号格式不正确，应使用语义化版本格式")
    private String version;

    /**
     * 服务端点URL
     */
    @Schema(description = "服务访问端点URL", example = "https://api.example.com/user-service", required = true, format = "uri")
    @NotBlank(message = "服务端点不能为空")
    private String endpoint;

    /**
     * Maven/Gradle坐标 - groupId
     */
    @Schema(description = "Maven/Gradle坐标的groupId", example = "com.example")
    @Pattern(regexp = "^[a-zA-Z][a-zA-Z0-9_]*(?:\\.[a-zA-Z][a-zA-Z0-9_]*)*$", 
             message = "groupId格式不正确")
    private String groupId;

    /**
     * Maven/Gradle坐标 - artifactId
     */
    @Schema(description = "Maven/Gradle坐标的artifactId", example = "user-service")
    @Pattern(regexp = "^[a-zA-Z][a-zA-Z0-9_-]*$", message = "artifactId格式不正确")
    private String artifactId;

    /**
     * 服务标签
     */
    @Schema(description = "服务标签列表，用于分类和搜索", 
            example = "[\"authentication\", \"user-management\", \"microservice\"]")
    @Size(max = 20, message = "标签数量不能超过20个")
    private List<@NotBlank(message = "标签不能为空") @Size(max = 50, message = "标签长度不能超过50字符") String> tags;

    /**
     * 服务状态
     */
    @Schema(description = "服务初始状态", example = "ACTIVE", 
            allowableValues = {"ACTIVE", "INACTIVE", "DEPRECATED", "MAINTENANCE", "UNKNOWN"})
    private String status = "ACTIVE";

    /**
     * 服务元数据
     */
    @Schema(description = "服务元数据键值对", 
            example = "{\"environment\": \"production\", \"region\": \"us-east-1\", \"maintainer\": \"<EMAIL>\"}")
    @Size(max = 50, message = "元数据项数量不能超过50个")
    private Map<@NotBlank(message = "元数据键不能为空") @Size(max = 100, message = "元数据键长度不能超过100字符") String, 
                @Size(max = 500, message = "元数据值长度不能超过500字符") String> metadata;
}