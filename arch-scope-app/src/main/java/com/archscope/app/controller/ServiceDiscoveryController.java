package com.archscope.app.controller;

import com.archscope.app.dto.FeedbackDTO;
import com.archscope.app.dto.RequirementDTO;
import com.archscope.app.dto.ServiceDTO;
import com.archscope.app.dto.ServiceQueryRequest;
import com.archscope.app.dto.ServiceRecommendationDTO;
import com.archscope.app.service.RequirementMatchingService;
import com.archscope.app.service.ServiceDiscoveryService;
import com.archscope.facade.dto.ApiResponse;
import com.archscope.facade.dto.PageResponseDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.responses.ApiResponse as SwaggerApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 服务发现控制器
 * 提供服务查询和发现的REST API
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/discovery")
@RequiredArgsConstructor
@Validated
@Tag(name = "服务发现", description = "服务查询和发现的REST API")
public class ServiceDiscoveryController {

    private final ServiceDiscoveryService serviceDiscoveryService;
    private final RequirementMatchingService requirementMatchingService;

    /**
     * 查找所有活跃服务
     *
     * @return 所有活跃服务列表
     */
    @Operation(
        summary = "查找所有活跃服务", 
        description = "获取系统中所有状态为活跃的服务列表。活跃服务是指当前可用且正在运行的服务。",
        tags = {"服务发现"}
    )
    @ApiResponses(value = {
        @SwaggerApiResponse(
            responseCode = "200", 
            description = "成功获取活跃服务列表",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = ApiResponse.class),
                examples = @ExampleObject(
                    name = "成功响应示例",
                    value = """
                        {
                            "success": true,
                            "message": "查找活跃服务成功",
                            "data": [
                                {
                                    "id": "srv-001",
                                    "name": "用户管理服务",
                                    "description": "提供用户注册、登录、权限管理等功能",
                                    "type": "REST_API",
                                    "version": "1.2.0",
                                    "endpoint": "https://api.example.com/user-service",
                                    "status": "ACTIVE",
                                    "registeredAt": "2024-01-15T10:30:00Z"
                                }
                            ],
                            "timestamp": "2024-01-20T15:30:00Z"
                        }
                        """
                )
            )
        ),
        @SwaggerApiResponse(
            responseCode = "500", 
            description = "服务器内部错误",
            content = @Content(
                mediaType = "application/json",
                examples = @ExampleObject(
                    name = "错误响应示例",
                    value = """
                        {
                            "success": false,
                            "message": "查找活跃服务失败",
                            "error": "数据库连接异常",
                            "timestamp": "2024-01-20T15:30:00Z"
                        }
                        """
                )
            )
        )
    })
    @GetMapping("/active")
    public ResponseEntity<ApiResponse<List<ServiceDTO>>> findAllActiveServices() {
        log.info("查找所有活跃服务请求");
        List<ServiceDTO> services = serviceDiscoveryService.findAllActiveServices();
        log.info("查找到{}个活跃服务", services.size());
        return ResponseEntity.ok(ApiResponse.success("查找活跃服务成功", services));
    }

    /**
     * 分页查询服务（支持排序）
     *
     * @param page 页码（从0开始）
     * @param size 每页大小（最大100）
     * @param sortBy 排序字段（默认为registeredAt）
     * @param sortDirection 排序方向（ASC/DESC，默认为DESC）
     * @return 分页的服务列表
     */
    @Operation(
        summary = "分页查询服务", 
        description = "支持分页和排序的服务查询接口。可以按照不同字段进行排序，支持升序和降序。",
        tags = {"服务发现"}
    )
    @ApiResponses(value = {
        @SwaggerApiResponse(
            responseCode = "200", 
            description = "成功获取分页服务列表",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = ApiResponse.class),
                examples = @ExampleObject(
                    name = "分页查询成功响应",
                    value = """
                        {
                            "success": true,
                            "message": "分页查询服务成功",
                            "data": {
                                "content": [
                                    {
                                        "id": "srv-001",
                                        "name": "用户管理服务",
                                        "type": "REST_API",
                                        "status": "ACTIVE"
                                    }
                                ],
                                "page": 0,
                                "size": 10,
                                "totalElements": 25,
                                "totalPages": 3,
                                "first": true,
                                "last": false
                            }
                        }
                        """
                )
            )
        ),
        @SwaggerApiResponse(
            responseCode = "400", 
            description = "请求参数错误",
            content = @Content(
                mediaType = "application/json",
                examples = @ExampleObject(
                    name = "参数错误响应",
                    value = """
                        {
                            "success": false,
                            "message": "请求参数错误",
                            "error": "页码不能小于0",
                            "timestamp": "2024-01-20T15:30:00Z"
                        }
                        """
                )
            )
        )
    })
    @GetMapping
    public ResponseEntity<ApiResponse<PageResponseDTO<ServiceDTO>>> findServices(
            @Parameter(description = "页码，从0开始", example = "0") 
            @RequestParam(defaultValue = "0") @Min(0) int page,
            @Parameter(description = "每页大小，最大100", example = "10") 
            @RequestParam(defaultValue = "10") @Min(1) @Max(100) int size,
            @Parameter(description = "排序字段", example = "registeredAt", 
                      schema = @Schema(allowableValues = {"name", "type", "status", "registeredAt", "lastUpdatedAt"})) 
            @RequestParam(defaultValue = "registeredAt") String sortBy,
            @Parameter(description = "排序方向", example = "DESC", 
                      schema = @Schema(allowableValues = {"ASC", "DESC"})) 
            @RequestParam(defaultValue = "DESC") String sortDirection) {
        log.info("分页查询服务请求: page={}, size={}, sortBy={}, sortDirection={}", 
                page, size, sortBy, sortDirection);
        
        // 限制最大页面大小以防止性能问题
        size = Math.min(size, 100);
        
        PageResponseDTO<ServiceDTO> services = serviceDiscoveryService.findServicesWithSort(page, size, sortBy, sortDirection);
        log.info("分页查询服务成功，共{}条记录", services.getTotalElements());
        return ResponseEntity.ok(ApiResponse.success("分页查询服务成功", services));
    }

    /**
     * 根据名称搜索服务
     *
     * @param name 服务名称（支持模糊匹配）
     * @return 匹配的服务列表
     */
    @GetMapping("/search/name")
    public ResponseEntity<ApiResponse<List<ServiceDTO>>> findServicesByName(
            @RequestParam @NotBlank(message = "服务名称不能为空") String name) {
        log.info("根据名称搜索服务请求: name={}", name);
        List<ServiceDTO> services = serviceDiscoveryService.findServicesByName(name);
        log.info("根据名称搜索到{}个服务", services.size());
        return ResponseEntity.ok(ApiResponse.success("根据名称搜索服务成功", services));
    }

    /**
     * 根据类型查找服务
     *
     * @param type 服务类型
     * @return 匹配的服务列表
     */
    @GetMapping("/search/type")
    public ResponseEntity<ApiResponse<List<ServiceDTO>>> findServicesByType(
            @RequestParam @NotBlank(message = "服务类型不能为空") String type) {
        log.info("根据类型查找服务请求: type={}", type);
        List<ServiceDTO> services = serviceDiscoveryService.findServicesByType(type);
        log.info("根据类型查找到{}个服务", services.size());
        return ResponseEntity.ok(ApiResponse.success("根据类型查找服务成功", services));
    }

    /**
     * 根据状态查找服务
     *
     * @param status 服务状态
     * @return 匹配的服务列表
     */
    @GetMapping("/search/status")
    public ResponseEntity<ApiResponse<List<ServiceDTO>>> findServicesByStatus(
            @RequestParam @NotBlank(message = "服务状态不能为空") String status) {
        log.info("根据状态查找服务请求: status={}", status);
        List<ServiceDTO> services = serviceDiscoveryService.findServicesByStatus(status);
        log.info("根据状态查找到{}个服务", services.size());
        return ResponseEntity.ok(ApiResponse.success("根据状态查找服务成功", services));
    }

    /**
     * 根据标签查找服务
     *
     * @param tags 标签列表
     * @return 匹配的服务列表
     */
    @GetMapping("/search/tags")
    public ResponseEntity<ApiResponse<List<ServiceDTO>>> findServicesByTags(
            @RequestParam List<String> tags) {
        log.info("根据标签查找服务请求: tags={}", tags);
        List<ServiceDTO> services = serviceDiscoveryService.findServicesByTags(tags);
        log.info("根据标签查找到{}个服务", services.size());
        return ResponseEntity.ok(ApiResponse.success("根据标签查找服务成功", services));
    }

    /**
     * 根据能力名称查找提供该能力的服务
     *
     * @param capabilityName 能力名称
     * @return 提供该能力的服务列表
     */
    @GetMapping("/search/capability")
    public ResponseEntity<ApiResponse<List<ServiceDTO>>> findServicesByCapability(
            @RequestParam @NotBlank(message = "能力名称不能为空") String capabilityName) {
        log.info("根据能力查找服务请求: capabilityName={}", capabilityName);
        List<ServiceDTO> services = serviceDiscoveryService.findServicesByCapability(capabilityName);
        log.info("根据能力查找到{}个服务", services.size());
        return ResponseEntity.ok(ApiResponse.success("根据能力查找服务成功", services));
    }

    /**
     * 根据Maven坐标查找服务（不指定版本）
     *
     * @param groupId Maven/Gradle坐标 - groupId
     * @param artifactId Maven/Gradle坐标 - artifactId
     * @return 匹配的服务列表
     */
    @GetMapping("/search/maven")
    public ResponseEntity<ApiResponse<List<ServiceDTO>>> findServicesByMavenCoordinates(
            @RequestParam @NotBlank(message = "groupId不能为空") String groupId,
            @RequestParam @NotBlank(message = "artifactId不能为空") String artifactId) {
        log.info("根据Maven坐标查找服务请求: groupId={}, artifactId={}", groupId, artifactId);
        List<ServiceDTO> services = serviceDiscoveryService.findServicesByMavenCoordinates(groupId, artifactId);
        log.info("根据Maven坐标查找到{}个服务", services.size());
        return ResponseEntity.ok(ApiResponse.success("根据Maven坐标查找服务成功", services));
    }

    /**
     * 根据精确的Maven坐标查找服务（包含版本）
     *
     * @param groupId Maven/Gradle坐标 - groupId
     * @param artifactId Maven/Gradle坐标 - artifactId
     * @param version 服务版本
     * @return 匹配的服务，如果不存在则返回null
     */
    @GetMapping("/search/maven/exact")
    public ResponseEntity<ApiResponse<ServiceDTO>> findServiceByExactMavenCoordinates(
            @RequestParam @NotBlank(message = "groupId不能为空") String groupId,
            @RequestParam @NotBlank(message = "artifactId不能为空") String artifactId,
            @RequestParam @NotBlank(message = "version不能为空") String version) {
        log.info("根据精确Maven坐标查找服务请求: groupId={}, artifactId={}, version={}", groupId, artifactId, version);
        ServiceDTO service = serviceDiscoveryService.findServiceByExactMavenCoordinates(groupId, artifactId, version);
        if (service != null) {
            log.info("根据精确Maven坐标找到服务: {}", service.getId());
            return ResponseEntity.ok(ApiResponse.success("根据精确Maven坐标查找服务成功", service));
        } else {
            log.info("根据精确Maven坐标未找到服务");
            return ResponseEntity.ok(ApiResponse.success("未找到匹配的服务", null));
        }
    }

    /**
     * 获取服务详细信息
     *
     * @param serviceId 服务ID
     * @return 服务详细信息
     */
    @GetMapping("/{serviceId}")
    @Operation(summary = "获取服务详细信息", description = "根据服务ID获取服务的详细信息")
    public ResponseEntity<ApiResponse<ServiceDTO>> getServiceById(
            @PathVariable @NotBlank(message = "服务ID不能为空") String serviceId) {
        log.info("获取服务详细信息请求: serviceId={}", serviceId);
        ServiceDTO service = serviceDiscoveryService.getServiceById(serviceId);
        if (service != null) {
            log.info("获取服务详细信息成功: {}", serviceId);
            return ResponseEntity.ok(ApiResponse.success("获取服务详细信息成功", service));
        } else {
            log.info("服务不存在: {}", serviceId);
            return ResponseEntity.ok(ApiResponse.success("服务不存在", null));
        }
    }

    /**
     * 获取服务统计信息
     *
     * @return 服务统计信息
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取服务统计信息", description = "获取系统中服务的统计信息")
    public ResponseEntity<ApiResponse<Object>> getServiceStatistics() {
        log.info("获取服务统计信息请求");
        Object statistics = serviceDiscoveryService.getServiceStatistics();
        log.info("获取服务统计信息成功");
        return ResponseEntity.ok(ApiResponse.success("获取服务统计信息成功", statistics));
    }

    /**
     * 组合条件查询服务（支持完整的过滤和排序）
     *
     * @param name 服务名称（可选，支持模糊匹配）
     * @param type 服务类型（可选）
     * @param status 服务状态（可选）
     * @param tags 标签列表（可选）
     * @param groupId Maven坐标groupId（可选）
     * @param artifactId Maven坐标artifactId（可选）
     * @param capabilityName 能力名称（可选）
     * @param page 页码（从0开始）
     * @param size 每页大小
     * @param sortBy 排序字段（默认为registeredAt）
     * @param sortDirection 排序方向（ASC/DESC，默认为DESC）
     * @return 分页的服务列表
     */
    @GetMapping("/search")
    public ResponseEntity<ApiResponse<PageResponseDTO<ServiceDTO>>> findServicesByCriteria(
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) List<String> tags,
            @RequestParam(required = false) String groupId,
            @RequestParam(required = false) String artifactId,
            @RequestParam(required = false) String capabilityName,
            @RequestParam(defaultValue = "0") @Min(0) int page,
            @RequestParam(defaultValue = "10") @Min(1) int size,
            @RequestParam(defaultValue = "registeredAt") String sortBy,
            @RequestParam(defaultValue = "DESC") String sortDirection) {
        log.info("组合条件查询服务请求: name={}, type={}, status={}, tags={}, groupId={}, artifactId={}, capabilityName={}, page={}, size={}, sortBy={}, sortDirection={}", 
                name, type, status, tags, groupId, artifactId, capabilityName, page, size, sortBy, sortDirection);
        
        ServiceQueryRequest queryRequest = ServiceQueryRequest.builder()
                .name(name)
                .type(type)
                .status(status)
                .tags(tags)
                .groupId(groupId)
                .artifactId(artifactId)
                .capabilityName(capabilityName)
                .page(page)
                .size(size)
                .sortBy(sortBy)
                .sortDirection(sortDirection)
                .build();
                
        PageResponseDTO<ServiceDTO> services = serviceDiscoveryService.findServicesByCriteria(queryRequest);
        log.info("组合条件查询服务成功，共{}条记录", services.getTotalElements());
        return ResponseEntity.ok(ApiResponse.success("组合条件查询服务成功", services));
    }

    /**
     * 使用POST方式进行复杂查询（支持请求体）
     *
     * @param queryRequest 查询请求对象
     * @return 分页的服务列表
     */
    @PostMapping("/search")
    @Operation(summary = "复杂条件查询服务", description = "使用POST方式支持复杂的查询条件和排序")
    public ResponseEntity<ApiResponse<PageResponseDTO<ServiceDTO>>> findServicesByComplexCriteria(
            @Valid @RequestBody ServiceQueryRequest queryRequest) {
        log.info("复杂条件查询服务请求: {}", queryRequest);
        PageResponseDTO<ServiceDTO> services = serviceDiscoveryService.findServicesByCriteria(queryRequest);
        log.info("复杂条件查询服务成功，共{}条记录", services.getTotalElements());
        return ResponseEntity.ok(ApiResponse.success("复杂条件查询服务成功", services));
    }

    // ==================== 需求匹配API ====================

    /**
     * 根据需求匹配服务
     *
     * @param requirement 需求信息
     * @return 匹配的服务推荐列表
     */
    @PostMapping("/match-requirement")
    @Operation(summary = "根据需求匹配服务", description = "根据需求信息匹配合适的服务并返回推荐列表")
    public ResponseEntity<ApiResponse<List<ServiceRecommendationDTO>>> matchServicesForRequirement(
            @Valid @RequestBody RequirementDTO requirement) {
        log.info("根据需求匹配服务请求: requirementId={}", requirement.getId());
        
        try {
            List<ServiceRecommendationDTO> recommendations = 
                requirementMatchingService.findServicesForRequirement(requirement);
            log.info("需求匹配成功，找到{}个推荐服务", recommendations.size());
            return ResponseEntity.ok(ApiResponse.success("需求匹配成功", recommendations));
        } catch (Exception e) {
            log.error("需求匹配失败: requirementId={}", requirement.getId(), e);
            throw e;
        }
    }

    /**
     * 根据需求描述生成能力需求
     *
     * @param description 需求描述
     * @return 生成的能力需求列表
     */
    @PostMapping("/generate-capabilities")
    @Operation(summary = "生成能力需求", description = "根据需求描述自动生成能力需求列表")
    public ResponseEntity<ApiResponse<List<String>>> generateCapabilityRequirements(
            @Parameter(description = "需求描述") @RequestParam @NotBlank(message = "需求描述不能为空") String description) {
        log.info("生成能力需求请求: description={}", description);
        
        try {
            List<String> capabilities = requirementMatchingService.generateCapabilityRequirements(description);
            log.info("生成能力需求成功，生成{}个能力", capabilities.size());
            return ResponseEntity.ok(ApiResponse.success("生成能力需求成功", capabilities));
        } catch (Exception e) {
            log.error("生成能力需求失败: description={}", description, e);
            throw e;
        }
    }

    /**
     * 记录推荐反馈
     *
     * @param recommendationId 推荐ID
     * @param feedback 反馈信息
     * @return 反馈记录
     */
    @PostMapping("/recommendations/{recommendationId}/feedback")
    @Operation(summary = "记录推荐反馈", description = "记录用户对服务推荐的反馈信息")
    public ResponseEntity<ApiResponse<FeedbackDTO>> recordRecommendationFeedback(
            @Parameter(description = "推荐ID") @PathVariable @NotBlank(message = "推荐ID不能为空") String recommendationId,
            @Valid @RequestBody FeedbackDTO feedback) {
        log.info("记录推荐反馈请求: recommendationId={}", recommendationId);
        
        try {
            FeedbackDTO result = requirementMatchingService.recordRecommendationFeedback(recommendationId, feedback);
            log.info("记录推荐反馈成功: feedbackId={}", result.getId());
            return ResponseEntity.ok(ApiResponse.success("记录推荐反馈成功", result));
        } catch (Exception e) {
            log.error("记录推荐反馈失败: recommendationId={}", recommendationId, e);
            throw e;
        }
    }
}