package com.archscope.app.controller;

import com.archscope.app.command.ServiceRegistrationCommand;
import com.archscope.app.command.ServiceUpdateCommand;
import com.archscope.app.dto.ServiceDTO;
import com.archscope.app.service.ServiceRegistryService;
import com.archscope.facade.dto.ApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

/**
 * 服务注册控制器
 * 提供服务注册、更新和删除的REST API
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/services")
@RequiredArgsConstructor
@Validated
public class ServiceRegistryController {

    private final ServiceRegistryService serviceRegistryService;

    /**
     * 注册服务
     *
     * @param command 服务注册命令
     * @return 注册成功的服务信息
     */
    @PostMapping
    public ResponseEntity<ApiResponse<ServiceDTO>> registerService(@Valid @RequestBody ServiceRegistrationCommand command) {
        log.info("注册服务请求: {}", command.getName());
        ServiceDTO serviceDTO = serviceRegistryService.registerService(command);
        log.info("服务注册成功，服务ID: {}", serviceDTO.getId());
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponse.success("服务注册成功", serviceDTO));
    }

    /**
     * 更新服务信息
     *
     * @param serviceId 服务ID
     * @param command 服务更新命令
     * @return 更新后的服务信息
     */
    @PutMapping("/{serviceId}")
    public ResponseEntity<ApiResponse<ServiceDTO>> updateService(
            @PathVariable @NotBlank(message = "服务ID不能为空") String serviceId,
            @Valid @RequestBody ServiceUpdateCommand command) {
        log.info("更新服务请求，服务ID: {}", serviceId);
        ServiceDTO serviceDTO = serviceRegistryService.updateService(serviceId, command);
        log.info("服务更新成功，服务ID: {}", serviceId);
        return ResponseEntity.ok(ApiResponse.success("服务更新成功", serviceDTO));
    }

    /**
     * 注销服务
     *
     * @param serviceId 服务ID
     * @return 操作结果
     */
    @DeleteMapping("/{serviceId}")
    public ResponseEntity<ApiResponse<Void>> deregisterService(
            @PathVariable @NotBlank(message = "服务ID不能为空") String serviceId) {
        log.info("注销服务请求，服务ID: {}", serviceId);
        serviceRegistryService.deregisterService(serviceId);
        log.info("服务注销成功，服务ID: {}", serviceId);
        return ResponseEntity.ok(ApiResponse.success("服务注销成功"));
    }

    /**
     * 根据ID获取服务详情
     *
     * @param serviceId 服务ID
     * @return 服务详情
     */
    @GetMapping("/{serviceId}")
    public ResponseEntity<ApiResponse<ServiceDTO>> getServiceById(
            @PathVariable @NotBlank(message = "服务ID不能为空") String serviceId) {
        log.info("获取服务详情请求，服务ID: {}", serviceId);
        ServiceDTO serviceDTO = serviceRegistryService.getServiceById(serviceId);
        return ResponseEntity.ok(ApiResponse.success("获取服务详情成功", serviceDTO));
    }
}