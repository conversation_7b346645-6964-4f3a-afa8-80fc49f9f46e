package com.archscope.app.controller;

import com.archscope.app.service.ProjectAppService;
import com.archscope.domain.service.GitRepositoryMetadataService;
import com.archscope.domain.valueobject.GitRepositoryInfo;
import com.archscope.domain.valueobject.GitRepositoryValidationRequest;
import com.archscope.facade.dto.GitRepositoryInfoDTO;
import com.archscope.facade.dto.GitRepositoryValidationRequestDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * Git仓库信息控制器
 * 提供Git仓库元数据获取相关的API接口
 *
 * <p>支持的Git托管平台：</p>
 * <ul>
 *   <li>GitHub (github.com)</li>
 *   <li>GitLab (gitlab.com)</li>
 *   <li>Bitbucket (bitbucket.org)</li>
 *   <li>其他标准Git仓库</li>
 * </ul>
 *
 * <p>支持的URL格式：</p>
 * <ul>
 *   <li>HTTPS: https://github.com/user/repo</li>
 *   <li>SSH: **************:user/repo.git</li>
 * </ul>
 */
@Slf4j
@Tag(name = "Git仓库管理", description = "Git仓库验证和信息获取接口")
@RestController
@RequestMapping("/api/git-repository")
@RequiredArgsConstructor
@Tag(name = "Git仓库管理", description = "Git仓库验证和信息获取接口")
public class GitRepositoryController {
    
    private final GitRepositoryMetadataService gitRepositoryMetadataService;
    private final ProjectAppService projectAppService;
    
    /**
     * 验证Git仓库URL并获取基本信息
     *
     * @param request 验证请求
     * @return Git仓库信息
     */
    @PostMapping("/validate")
    public ResponseEntity<GitRepositoryInfoDTO> validateRepository(
            @Valid @RequestBody GitRepositoryValidationRequestDTO request) {
        
        log.info("收到Git仓库验证请求: {}", request.getRepositoryUrl());
        
        try {
            // 转换DTO到领域对象
            GitRepositoryValidationRequest domainRequest = convertToDomainRequest(request);
            GitRepositoryInfo domainResult = gitRepositoryMetadataService
                .validateAndFetchRepositoryInfo(domainRequest);

            // 转换领域对象到DTO
            GitRepositoryInfoDTO result = convertToDTO(domainResult);
            
            if (result.getSuccess()) {
                log.info("Git仓库验证成功: {}", request.getRepositoryUrl());
                return ResponseEntity.ok(result);
            } else {
                log.warn("Git仓库验证失败: {}, 错误: {}", 
                    request.getRepositoryUrl(), result.getErrorMessage());
                return ResponseEntity.badRequest().body(result);
            }
            
        } catch (Exception e) {
            log.error("Git仓库验证异常: {}", request.getRepositoryUrl(), e);
            
            GitRepositoryInfoDTO errorResult = GitRepositoryInfoDTO.builder()
                .success(false)
                .errorMessage("服务器内部错误: " + e.getMessage())
                .build();
            
            return ResponseEntity.internalServerError().body(errorResult);
        }
    }
    
    /**
     * 快速验证Git仓库URL格式
     * 
     * @param repositoryUrl Git仓库URL
     * @return 验证结果
     */
    @GetMapping("/validate-url")
    public ResponseEntity<Boolean> validateUrl(@RequestParam String repositoryUrl) {
        
        log.debug("快速验证Git仓库URL: {}", repositoryUrl);
        
        boolean isValid = gitRepositoryMetadataService.isValidGitUrl(repositoryUrl);
        return ResponseEntity.ok(isValid);
    }
    
    /**
     * 获取Git仓库详细信息（包括分支列表和项目重复性检查）
     *
     * @param repositoryUrl Git仓库URL
     * @param username 用户名（可选，用于私有仓库）
     * @param password 密码或Token（可选，用于私有仓库）
     * @return Git仓库详细信息
     */
    @GetMapping("/details")
    public ResponseEntity<GitRepositoryInfoDTO> getRepositoryDetails(
            @RequestParam String repositoryUrl,
            @RequestParam(required = false) String username,
            @RequestParam(required = false) String password) {

        log.info("获取Git仓库详细信息: {}", repositoryUrl);

        try {
            // 1. 首先进行项目重复性检查
            boolean repositoryExists = projectAppService.isRepositoryUrlExists(repositoryUrl);
            if (repositoryExists) {
                log.warn("仓库已存在: {}", repositoryUrl);
                GitRepositoryInfoDTO errorResult = GitRepositoryInfoDTO.builder()
                    .success(false)
                    .errorMessage("该仓库已被其他项目注册使用，请检查项目列表或使用不同的仓库")
                    .build();
                return ResponseEntity.badRequest().body(errorResult);
            }

            // 2. 获取仓库详细信息
            GitRepositoryValidationRequestDTO request = GitRepositoryValidationRequestDTO.builder()
                .repositoryUrl(repositoryUrl)
                .fetchDetails(true)
                .username(username)
                .password(password)
                .build();

            // 转换DTO到领域对象
            GitRepositoryValidationRequest domainRequest = convertToDomainRequest(request);
            GitRepositoryInfo domainResult = gitRepositoryMetadataService
                .validateAndFetchRepositoryInfo(domainRequest);

            // 转换领域对象到DTO
            GitRepositoryInfoDTO result = convertToDTO(domainResult);

            if (result.getSuccess()) {
                log.info("Git仓库详细信息获取成功: {}", repositoryUrl);
                return ResponseEntity.ok(result);
            } else {
                log.warn("Git仓库详细信息获取失败: {}, 错误: {}", repositoryUrl, result.getErrorMessage());
                return ResponseEntity.badRequest().body(result);
            }

        } catch (Exception e) {
            log.error("获取Git仓库详细信息异常: {}", repositoryUrl, e);

            GitRepositoryInfoDTO errorResult = GitRepositoryInfoDTO.builder()
                .success(false)
                .errorMessage("获取详细信息失败: " + e.getMessage())
                .build();

            return ResponseEntity.internalServerError().body(errorResult);
        }
    }

    /**
     * 从Git仓库URL提取项目名称
     *
     * @param repositoryUrl Git仓库URL
     * @return 项目名称
     */
    @GetMapping("/extract-project-name")
    public ResponseEntity<String> extractProjectName(@RequestParam String repositoryUrl) {

        log.debug("提取项目名称: {}", repositoryUrl);

        try {
            String projectName = gitRepositoryMetadataService.extractProjectNameFromUrl(repositoryUrl);
            return ResponseEntity.ok(projectName);
        } catch (Exception e) {
            log.error("提取项目名称失败: {}", repositoryUrl, e);
            return ResponseEntity.badRequest().body("unknown-project");
        }
    }

    /**
     * 转换DTO到领域对象
     */
    private GitRepositoryValidationRequest convertToDomainRequest(GitRepositoryValidationRequestDTO dto) {
        return GitRepositoryValidationRequest.builder()
            .repositoryUrl(dto.getRepositoryUrl())
            .fetchDetails(dto.getFetchDetails())
            .username(dto.getUsername())
            .password(dto.getPassword())
            .build();
    }

    /**
     * 转换领域对象到DTO
     */
    private GitRepositoryInfoDTO convertToDTO(GitRepositoryInfo domain) {
        return GitRepositoryInfoDTO.builder()
            .projectName(domain.getProjectName())
            .description(domain.getDescription())
            .defaultBranch(domain.getDefaultBranch())
            .branches(domain.getBranches())
            .repositoryType(domain.getRepositoryType())
            .owner(domain.getOwner())
            .repositoryName(domain.getRepositoryName())
            .isPrivate(domain.getIsPrivate())
            .languages(domain.getLanguages())
            .createdAt(domain.getCreatedAt())
            .updatedAt(domain.getUpdatedAt())
            .size(domain.getSize())
            .starCount(domain.getStarCount())
            .forkCount(domain.getForkCount())
            .success(domain.getSuccess())
            .errorMessage(domain.getErrorMessage())
            .build();
    }
}
