package com.archscope.app.controller;

import com.archscope.app.command.CapabilityRegistrationCommand;
import com.archscope.app.dto.CapabilityDTO;
import com.archscope.app.service.CapabilityManagementService;
import com.archscope.facade.dto.ApiResponse;
import com.archscope.facade.dto.PageResponseDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.responses.ApiResponse as SwaggerApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 能力管理控制器
 * 专门处理服务能力的注册、查询和管理操作
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/capabilities")
@RequiredArgsConstructor
@Validated
@Tag(name = "能力管理", description = "服务能力注册、查询和管理相关操作")
public class CapabilityManagementController {

    private final CapabilityManagementService capabilityManagementService;

    @Operation(
        summary = "注册服务能力", 
        description = "为服务注册新的能力。能力描述了服务可以提供的具体功能，包括输入输出示例和使用说明。",
        tags = {"能力管理"}
    )
    @ApiResponses(value = {
        @SwaggerApiResponse(
            responseCode = "201", 
            description = "能力注册成功",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = ApiResponse.class),
                examples = @ExampleObject(
                    name = "能力注册成功响应",
                    value = """
                        {
                            "success": true,
                            "message": "服务能力注册成功",
                            "data": {
                                "id": "cap-001",
                                "name": "用户认证",
                                "description": "提供用户登录认证功能",
                                "version": "1.0.0",
                                "examples": [
                                    {
                                        "name": "用户名密码登录",
                                        "description": "使用用户名和密码进行登录认证",
                                        "requestExample": "{\\"username\\": \\"john\\", \\"password\\": \\"secret\\"}",
                                        "responseExample": "{\\"token\\": \\"jwt-token\\", \\"userId\\": \\"123\\"}"
                                    }
                                ],
                                "deprecated": false,
                                "createdAt": "2024-01-20T15:30:00Z"
                            }
                        }
                        """
                )
            )
        ),
        @SwaggerApiResponse(
            responseCode = "400", 
            description = "请求参数错误",
            content = @Content(
                mediaType = "application/json",
                examples = @ExampleObject(
                    name = "参数错误响应",
                    value = """
                        {
                            "success": false,
                            "message": "请求参数错误",
                            "error": "能力名称不能为空",
                            "timestamp": "2024-01-20T15:30:00Z"
                        }
                        """
                )
            )
        )
    })
    @PostMapping
    public ResponseEntity<ApiResponse<CapabilityDTO>> registerCapability(
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                description = "能力注册信息",
                required = true,
                content = @Content(
                    mediaType = "application/json",
                    schema = @Schema(implementation = CapabilityRegistrationCommand.class),
                    examples = @ExampleObject(
                        name = "能力注册请求示例",
                        value = """
                            {
                                "name": "用户认证",
                                "description": "提供用户登录认证功能，支持用户名密码和第三方OAuth认证",
                                "version": "1.0.0",
                                "examples": [
                                    {
                                        "name": "用户名密码登录",
                                        "description": "使用用户名和密码进行登录认证",
                                        "requestExample": "{\\"username\\": \\"john\\", \\"password\\": \\"secret\\"}",
                                        "responseExample": "{\\"token\\": \\"jwt-token\\", \\"userId\\": \\"123\\", \\"expiresIn\\": 3600}"
                                    }
                                ],
                                "tags": ["authentication", "security", "login"]
                            }
                            """
                    )
                )
            )
            @Valid @RequestBody CapabilityRegistrationCommand command) {
        
        log.info("注册服务能力请求: name={}", command.getName());
        
        try {
            CapabilityDTO capability = capabilityManagementService.registerCapability(command);
            log.info("服务能力注册成功，能力ID: {}", capability.getId());
            return ResponseEntity.status(HttpStatus.CREATED)
                    .body(ApiResponse.success("服务能力注册成功", capability));
        } catch (Exception e) {
            log.error("服务能力注册失败: name={}", command.getName(), e);
            throw e;
        }
    }

    @Operation(summary = "查询服务能力", description = "根据关键词查询服务能力")
    @GetMapping("/search")
    @Cacheable(value = "capability-search", key = "#keyword + '_' + #page + '_' + #size")
    public ResponseEntity<ApiResponse<PageResponseDTO<CapabilityDTO>>> searchCapabilities(
            @Parameter(description = "搜索关键词") @RequestParam @NotBlank(message = "搜索关键词不能为空") String keyword,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") @Min(1) Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") @Min(1) Integer size) {
        
        log.info("查询服务能力请求: keyword={}, page={}, size={}", keyword, page, size);
        
        try {
            PageResponseDTO<CapabilityDTO> result = capabilityManagementService.searchCapabilities(keyword, page, size);
            log.info("查询服务能力成功，找到{}个结果", result.getTotal());
            return ResponseEntity.ok(ApiResponse.success("查询服务能力成功", result));
        } catch (Exception e) {
            log.error("查询服务能力失败: keyword={}", keyword, e);
            throw e;
        }
    }

    @Operation(summary = "获取服务的所有能力", description = "根据服务ID获取该服务的所有能力")
    @GetMapping("/by-service/{serviceId}")
    @Cacheable(value = "service-capabilities", key = "#serviceId")
    public ResponseEntity<ApiResponse<List<CapabilityDTO>>> getCapabilitiesByService(
            @Parameter(description = "服务ID") @PathVariable @NotBlank(message = "服务ID不能为空") String serviceId) {
        
        log.info("获取服务能力请求: serviceId={}", serviceId);
        
        try {
            List<CapabilityDTO> capabilities = capabilityManagementService.getCapabilitiesByService(serviceId);
            log.info("获取服务能力成功，找到{}个能力", capabilities.size());
            return ResponseEntity.ok(ApiResponse.success("获取服务能力成功", capabilities));
        } catch (Exception e) {
            log.error("获取服务能力失败: serviceId={}", serviceId, e);
            throw e;
        }
    }

    @Operation(summary = "获取能力详情", description = "根据能力ID获取能力的详细信息")
    @GetMapping("/{capabilityId}")
    public ResponseEntity<ApiResponse<CapabilityDTO>> getCapabilityById(
            @Parameter(description = "能力ID") @PathVariable @NotBlank(message = "能力ID不能为空") String capabilityId) {
        
        log.info("获取能力详情请求: capabilityId={}", capabilityId);
        
        try {
            CapabilityDTO capability = capabilityManagementService.getCapabilityById(capabilityId);
            return ResponseEntity.ok(ApiResponse.success("获取能力详情成功", capability));
        } catch (Exception e) {
            log.error("获取能力详情失败: capabilityId={}", capabilityId, e);
            throw e;
        }
    }

    @Operation(summary = "删除服务能力", description = "删除指定的服务能力")
    @DeleteMapping("/{capabilityId}")
    public ResponseEntity<ApiResponse<Void>> deleteCapability(
            @Parameter(description = "能力ID") @PathVariable @NotBlank(message = "能力ID不能为空") String capabilityId) {
        
        log.info("删除服务能力请求: capabilityId={}", capabilityId);
        
        try {
            capabilityManagementService.deleteCapability(capabilityId);
            log.info("删除服务能力成功: capabilityId={}", capabilityId);
            return ResponseEntity.ok(ApiResponse.success("删除服务能力成功"));
        } catch (Exception e) {
            log.error("删除服务能力失败: capabilityId={}", capabilityId, e);
            throw e;
        }
    }
}