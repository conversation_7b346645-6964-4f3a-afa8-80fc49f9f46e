package com.archscope.app.controller;

import com.archscope.app.dto.servicediscovery.CapabilityDTO;
import com.archscope.app.dto.servicediscovery.ServiceDTO;
import com.archscope.app.service.ServiceDiscoveryBatchService;
import com.archscope.facade.dto.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 服务发现批量操作控制器
 */
@RestController
@RequestMapping("/api/v1/service-discovery/batch")
@Slf4j
@Validated
@Tag(name = "服务发现批量操作", description = "服务发现批量操作相关API")
public class ServiceDiscoveryBatchController {

    private final ServiceDiscoveryBatchService batchService;

    public ServiceDiscoveryBatchController(ServiceDiscoveryBatchService batchService) {
        this.batchService = batchService;
    }

    @Operation(summary = "批量注册服务", description = "批量注册多个服务")
    @PostMapping("/services")
    public ResponseEntity<ApiResponse<List<ServiceDTO>>> batchRegisterServices(
            @Valid @RequestBody @NotEmpty List<ServiceDTO> services) {
        
        try {
            log.info("批量注册服务，数量: {}", services.size());
            List<ServiceDTO> registeredServices = batchService.batchRegisterServices(services);
            log.info("批量注册服务成功，注册数量: {}", registeredServices.size());
            return ResponseEntity.ok(ApiResponse.success("批量注册服务成功", registeredServices));
        } catch (Exception e) {
            log.error("批量注册服务失败", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("批量注册服务失败: " + e.getMessage()));
        }
    }

    @Operation(summary = "批量更新服务", description = "批量更新多个服务")
    @PutMapping("/services")
    public ResponseEntity<ApiResponse<List<ServiceDTO>>> batchUpdateServices(
            @Valid @RequestBody @NotEmpty List<ServiceDTO> services) {
        
        try {
            log.info("批量更新服务，数量: {}", services.size());
            List<ServiceDTO> updatedServices = batchService.batchUpdateServices(services);
            log.info("批量更新服务成功，更新数量: {}", updatedServices.size());
            return ResponseEntity.ok(ApiResponse.success("批量更新服务成功", updatedServices));
        } catch (Exception e) {
            log.error("批量更新服务失败", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("批量更新服务失败: " + e.getMessage()));
        }
    }

    @Operation(summary = "批量删除服务", description = "批量删除多个服务")
    @DeleteMapping("/services")
    public ResponseEntity<ApiResponse<Boolean>> batchDeleteServices(
            @RequestBody @NotEmpty List<String> serviceIds) {
        
        try {
            log.info("批量删除服务，数量: {}", serviceIds.size());
            boolean result = batchService.batchDeleteServices(serviceIds);
            log.info("批量删除服务完成，结果: {}", result);
            return ResponseEntity.ok(ApiResponse.success("批量删除服务完成", result));
        } catch (Exception e) {
            log.error("批量删除服务失败", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("批量删除服务失败: " + e.getMessage()));
        }
    }

    @Operation(summary = "批量查询服务", description = "根据服务ID列表批量查询服务")
    @PostMapping("/services/query")
    public ResponseEntity<ApiResponse<List<ServiceDTO>>> batchGetServices(
            @RequestBody @NotEmpty List<String> serviceIds) {
        
        try {
            log.info("批量查询服务，数量: {}", serviceIds.size());
            List<ServiceDTO> services = batchService.batchGetServices(serviceIds);
            log.info("批量查询服务成功，找到数量: {}", services.size());
            return ResponseEntity.ok(ApiResponse.success("批量查询服务成功", services));
        } catch (Exception e) {
            log.error("批量查询服务失败", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("批量查询服务失败: " + e.getMessage()));
        }
    }

    @Operation(summary = "批量注册能力", description = "批量注册多个能力")
    @PostMapping("/capabilities")
    public ResponseEntity<ApiResponse<List<CapabilityDTO>>> batchRegisterCapabilities(
            @Valid @RequestBody @NotEmpty List<CapabilityDTO> capabilities) {
        
        try {
            log.info("批量注册能力，数量: {}", capabilities.size());
            List<CapabilityDTO> registeredCapabilities = batchService.batchRegisterCapabilities(capabilities);
            log.info("批量注册能力成功，注册数量: {}", registeredCapabilities.size());
            return ResponseEntity.ok(ApiResponse.success("批量注册能力成功", registeredCapabilities));
        } catch (Exception e) {
            log.error("批量注册能力失败", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("批量注册能力失败: " + e.getMessage()));
        }
    }

    @Operation(summary = "批量更新能力", description = "批量更新多个能力")
    @PutMapping("/capabilities")
    public ResponseEntity<ApiResponse<List<CapabilityDTO>>> batchUpdateCapabilities(
            @Valid @RequestBody @NotEmpty List<CapabilityDTO> capabilities) {
        
        try {
            log.info("批量更新能力，数量: {}", capabilities.size());
            List<CapabilityDTO> updatedCapabilities = batchService.batchUpdateCapabilities(capabilities);
            log.info("批量更新能力成功，更新数量: {}", updatedCapabilities.size());
            return ResponseEntity.ok(ApiResponse.success("批量更新能力成功", updatedCapabilities));
        } catch (Exception e) {
            log.error("批量更新能力失败", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("批量更新能力失败: " + e.getMessage()));
        }
    }

    @Operation(summary = "批量删除能力", description = "批量删除多个能力")
    @DeleteMapping("/capabilities")
    public ResponseEntity<ApiResponse<Boolean>> batchDeleteCapabilities(
            @RequestBody @NotEmpty List<String> capabilityIds) {
        
        try {
            log.info("批量删除能力，数量: {}", capabilityIds.size());
            boolean result = batchService.batchDeleteCapabilities(capabilityIds);
            log.info("批量删除能力完成，结果: {}", result);
            return ResponseEntity.ok(ApiResponse.success("批量删除能力完成", result));
        } catch (Exception e) {
            log.error("批量删除能力失败", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("批量删除能力失败: " + e.getMessage()));
        }
    }

    @Operation(summary = "批量查询能力", description = "根据能力ID列表批量查询能力")
    @PostMapping("/capabilities/query")
    public ResponseEntity<ApiResponse<List<CapabilityDTO>>> batchGetCapabilities(
            @RequestBody @NotEmpty List<String> capabilityIds) {
        
        try {
            log.info("批量查询能力，数量: {}", capabilityIds.size());
            List<CapabilityDTO> capabilities = batchService.batchGetCapabilities(capabilityIds);
            log.info("批量查询能力成功，找到数量: {}", capabilities.size());
            return ResponseEntity.ok(ApiResponse.success("批量查询能力成功", capabilities));
        } catch (Exception e) {
            log.error("批量查询能力失败", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("批量查询能力失败: " + e.getMessage()));
        }
    }

    @Operation(summary = "批量查询服务能力", description = "根据服务ID列表批量查询所有相关能力")
    @PostMapping("/capabilities/by-services")
    public ResponseEntity<ApiResponse<List<CapabilityDTO>>> batchGetCapabilitiesByServiceIds(
            @RequestBody @NotEmpty List<String> serviceIds) {
        
        try {
            log.info("批量查询服务能力，服务数量: {}", serviceIds.size());
            List<CapabilityDTO> capabilities = batchService.batchGetCapabilitiesByServiceIds(serviceIds);
            log.info("批量查询服务能力成功，找到能力数量: {}", capabilities.size());
            return ResponseEntity.ok(ApiResponse.success("批量查询服务能力成功", capabilities));
        } catch (Exception e) {
            log.error("批量查询服务能力失败", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("批量查询服务能力失败: " + e.getMessage()));
        }
    }

    @Operation(summary = "批量导入服务和能力", description = "批量导入服务及其相关能力")
    @PostMapping("/import")
    public ResponseEntity<ApiResponse<String>> batchImportServicesWithCapabilities(
            @Valid @RequestBody @NotEmpty List<ServiceDTO> services) {
        
        try {
            log.info("批量导入服务和能力，服务数量: {}", services.size());
            batchService.batchImportServicesWithCapabilities(services);
            log.info("批量导入服务和能力成功");
            return ResponseEntity.ok(ApiResponse.success("批量导入服务和能力成功", "导入完成"));
        } catch (Exception e) {
            log.error("批量导入服务和能力失败", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("批量导入服务和能力失败: " + e.getMessage()));
        }
    }

    @Operation(summary = "批量导出服务和能力", description = "批量导出服务及其相关能力")
    @PostMapping("/export")
    public ResponseEntity<ApiResponse<List<ServiceDTO>>> batchExportServicesWithCapabilities(
            @RequestBody @NotEmpty List<String> serviceIds) {
        
        try {
            log.info("批量导出服务和能力，服务数量: {}", serviceIds.size());
            List<ServiceDTO> services = batchService.batchExportServicesWithCapabilities(serviceIds);
            log.info("批量导出服务和能力成功，导出数量: {}", services.size());
            return ResponseEntity.ok(ApiResponse.success("批量导出服务和能力成功", services));
        } catch (Exception e) {
            log.error("批量导出服务和能力失败", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("批量导出服务和能力失败: " + e.getMessage()));
        }
    }
}