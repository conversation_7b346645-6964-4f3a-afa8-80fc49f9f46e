package com.archscope.app.controller;

import com.archscope.domain.entity.DocumentVersion;
import com.archscope.domain.repository.DocumentVersionRepository;

import com.archscope.domain.service.MarkdownService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;


import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 文档内容控制器
 * 提供文档内容检索和渲染的REST API
 */
@Tag(name = "文档内容管理", description = "文档内容检索和渲染接口")
@RestController
@RequestMapping("/api/documents")

public class DocumentController {

    private final MarkdownService markdownService;
    private final DocumentVersionRepository documentVersionRepository;

    public DocumentController(MarkdownService markdownService,
                            DocumentVersionRepository documentVersionRepository) {
        this.markdownService = markdownService;
        this.documentVersionRepository = documentVersionRepository;
    }

    /**
     * 获取项目的文档类型列表
     *
     * @param projectId 项目ID
     * @param version 版本标签，可选
     * @return 文档类型列表
     */
    @GetMapping("/projects/{projectId}/types")
    @Operation(summary = "获取项目文档类型", description = "获取指定项目的所有可用文档类型")
    public ResponseEntity<List<String>> getDocumentTypes(
            @Parameter(description = "项目ID") @PathVariable Long projectId,
            @Parameter(description = "版本标签") @RequestParam(required = false) String version) {

        System.out.println("获取项目文档类型: projectId=" + projectId + ", version=" + version);

        try {
            // 直接从数据库查询文档类型
            List<DocumentVersion> documentVersions = documentVersionRepository.findByProjectId(projectId);
            List<String> documentTypes = documentVersions.stream()
                    .filter(dv -> version == null || version.equals(dv.getVersionTag()))
                    .map(dv -> dv.getDocType() != null ? dv.getDocType().name() : "UNKNOWN")
                    .distinct()
                    .sorted()
                    .collect(Collectors.toList());

            System.out.println("找到文档类型: " + documentTypes);
            return ResponseEntity.ok(documentTypes);
        } catch (Exception e) {
            System.err.println("获取项目文档类型失败: projectId=" + projectId + ", version=" + version + ", error=" + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 获取项目的版本列表
     *
     * @param projectId 项目ID
     * @return 版本列表
     */
    @GetMapping("/projects/{projectId}/versions")
    @Operation(summary = "获取项目版本列表", description = "获取指定项目的所有可用版本")
    public ResponseEntity<List<String>> getProjectVersions(
            @Parameter(description = "项目ID") @PathVariable Long projectId) {

        System.out.println("获取项目版本列表: projectId=" + projectId);

        try {
            // 直接从数据库查询版本列表
            List<DocumentVersion> documentVersions = documentVersionRepository.findByProjectId(projectId);
            List<String> versions = documentVersions.stream()
                    .map(DocumentVersion::getVersionTag)
                    .distinct()
                    .sorted()
                    .collect(Collectors.toList());

            System.out.println("找到版本列表: " + versions);
            return ResponseEntity.ok(versions);
        } catch (Exception e) {
            System.err.println("获取项目版本列表失败: projectId=" + projectId + ", error=" + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 获取文档的原始Markdown内容
     *
     * @param projectId 项目ID
     * @param docType 文档类型
     * @param version 版本标签，可选
     * @return 文档的Markdown内容
     */
    @GetMapping("/projects/{projectId}/documents/{docType}/content")
    @Operation(summary = "获取文档内容", description = "获取指定文档的原始Markdown内容")
    public ResponseEntity<String> getDocumentContent(
            @Parameter(description = "项目ID") @PathVariable Long projectId,
            @Parameter(description = "文档类型") @PathVariable String docType,
            @Parameter(description = "版本标签") @RequestParam(required = false) String version) {
        
        System.out.println("获取文档内容: projectId=" + projectId + ", docType=" + docType + ", version=" + version);

        try {
            // 直接从数据库查询文档内容
            List<DocumentVersion> documentVersions = documentVersionRepository.findByProjectId(projectId);

            for (DocumentVersion docVersion : documentVersions) {
                if (docVersion.getDocType() != null &&
                    docVersion.getDocType().name().equals(docType) &&
                    (version == null || version.equals(docVersion.getVersionTag()))) {

                    // 从compare_metadata字段获取内容
                    if (docVersion.getCompareMetadata() != null) {
                        Object contentObj = docVersion.getCompareMetadata().get("content");
                        if (contentObj instanceof String) {
                            System.out.println("找到文档内容，长度: " + ((String) contentObj).length());
                            return ResponseEntity.ok((String) contentObj);
                        }
                    }
                }
            }

            System.out.println("文档不存在: projectId=" + projectId + ", docType=" + docType + ", version=" + version);
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            System.err.println("获取文档内容失败: projectId=" + projectId + ", docType=" + docType + ", version=" + version + ", error=" + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 获取文档的HTML渲染内容
     *
     * @param projectId 项目ID
     * @param docType 文档类型
     * @param version 版本标签，可选
     * @return 文档的HTML内容
     */
    @GetMapping("/projects/{projectId}/documents/{docType}/html")
    @Operation(summary = "获取文档HTML", description = "获取指定文档渲染后的HTML内容")
    public ResponseEntity<String> getDocumentHtml(
            @Parameter(description = "项目ID") @PathVariable Long projectId,
            @Parameter(description = "文档类型") @PathVariable String docType,
            @Parameter(description = "版本标签") @RequestParam(required = false) String version) {
        
        System.out.println("获取文档HTML: projectId=" + projectId + ", docType=" + docType + ", version=" + version);

        try {
            // 直接从数据库查询文档内容
            List<DocumentVersion> documentVersions = documentVersionRepository.findByProjectId(projectId);

            for (DocumentVersion docVersion : documentVersions) {
                if (docVersion.getDocType() != null &&
                    docVersion.getDocType().name().equals(docType) &&
                    (version == null || version.equals(docVersion.getVersionTag()))) {

                    // 从compare_metadata字段获取内容
                    if (docVersion.getCompareMetadata() != null) {
                        Object contentObj = docVersion.getCompareMetadata().get("content");
                        if (contentObj instanceof String) {
                            String markdownContent = (String) contentObj;
                            String htmlContent = markdownService.convertToHtml(markdownContent);
                            System.out.println("转换HTML成功，长度: " + htmlContent.length());
                            return ResponseEntity.ok(htmlContent);
                        }
                    }
                }
            }

            System.out.println("文档不存在: projectId=" + projectId + ", docType=" + docType + ", version=" + version);
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            System.err.println("获取文档HTML失败: projectId=" + projectId + ", docType=" + docType + ", version=" + version + ", error=" + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 检查文档是否存在
     *
     * @param projectId 项目ID
     * @param docType 文档类型
     * @param version 版本标签，可选
     * @return 文档是否存在
     */
    @GetMapping("/projects/{projectId}/documents/{docType}/exists")
    @Operation(summary = "检查文档是否存在", description = "检查指定文档是否存在")
    public ResponseEntity<Boolean> documentExists(
            @Parameter(description = "项目ID") @PathVariable Long projectId,
            @Parameter(description = "文档类型") @PathVariable String docType,
            @Parameter(description = "版本标签") @RequestParam(required = false) String version) {
        
        System.out.println("检查文档是否存在: projectId=" + projectId + ", docType=" + docType + ", version=" + version);

        try {
            // 直接从数据库查询文档是否存在
            List<DocumentVersion> documentVersions = documentVersionRepository.findByProjectId(projectId);

            boolean exists = documentVersions.stream()
                    .anyMatch(dv -> dv.getDocType() != null &&
                                   dv.getDocType().name().equals(docType) &&
                                   (version == null || version.equals(dv.getVersionTag())));

            System.out.println("文档存在性检查结果: " + exists);
            return ResponseEntity.ok(exists);
        } catch (Exception e) {
            System.err.println("检查文档存在性失败: projectId=" + projectId + ", docType=" + docType + ", version=" + version + ", error=" + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.internalServerError().build();
        }
    }
}
