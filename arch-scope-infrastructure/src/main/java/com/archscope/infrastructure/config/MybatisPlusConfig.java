package com.archscope.infrastructure.config;

import com.archscope.domain.model.parser.FileParseResult;
import com.archscope.infrastructure.config.typehandler.JsonTypeHandler;
import com.archscope.infrastructure.config.typehandler.MapTypeHandler;
import com.archscope.infrastructure.config.typehandler.ProjectTypeHandler;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@Configuration
@EnableTransactionManagement
@MapperScan("com.archscope.infrastructure.mapper")
public class MybatisPlusConfig {

    /**
     * 注册Map类型处理器
     */
    @Bean
    public MapTypeHandler mapTypeHandler() {
        return new MapTypeHandler();
    }

    /**
     * 注册FileParseResult列表类型处理器
     */
    @Bean
    public JsonTypeHandler.ListTypeHandler<FileParseResult> fileParseResultListTypeHandler() {
        return new JsonTypeHandler.ListTypeHandler<>(FileParseResult.class);
    }

    /**
     * 注册ProjectType枚举类型处理器
     */
    @Bean
    public ProjectTypeHandler projectTypeHandler() {
        return new ProjectTypeHandler();
    }

    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        // 添加分页插件
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor());
        // 添加乐观锁插件
        interceptor.addInnerInterceptor(new OptimisticLockerInnerInterceptor());
        return interceptor;
    }


}