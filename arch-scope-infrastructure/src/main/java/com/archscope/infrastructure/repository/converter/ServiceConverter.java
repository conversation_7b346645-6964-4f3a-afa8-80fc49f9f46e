package com.archscope.infrastructure.repository.converter;

import com.archscope.domain.model.servicediscovery.Service;
import com.archscope.domain.model.servicediscovery.ServiceStatus;
import com.archscope.domain.model.servicediscovery.ServiceType;
import com.archscope.domain.valueobject.ServiceId;
import com.archscope.infrastructure.persistence.entity.ServiceDO;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 服务领域对象与数据对象转换器
 */
@Component
public class ServiceConverter {

    /**
     * 将领域对象转换为数据对象
     */
    public ServiceDO toDataObject(Service service) {
        if (service == null) {
            return null;
        }

        ServiceDO serviceDO = new ServiceDO();
        serviceDO.setServiceId(service.getId().getValue());
        serviceDO.setName(service.getName());
        serviceDO.setDescription(service.getDescription());
        serviceDO.setVersion(service.getVersion());
        serviceDO.setType(service.getType().name());
        serviceDO.setEndpoint(service.getEndpoint());
        serviceDO.setGroupId(service.getGroupId());
        serviceDO.setArtifactId(service.getArtifactId());
        serviceDO.setTags(new ArrayList<>(service.getTags()));
        serviceDO.setOwner(service.getOwner());
        serviceDO.setApiDocUrl(service.getApiDocUrl());
        serviceDO.setStatus(service.getStatus().name());
        serviceDO.setMetadata(new HashMap<>(service.getMetadata()));
        serviceDO.setCreatedAt(service.getCreatedAt());
        serviceDO.setUpdatedAt(service.getUpdatedAt());

        return serviceDO;
    }

    /**
     * 将数据对象转换为领域对象
     */
    public Service toDomainObject(ServiceDO serviceDO) {
        if (serviceDO == null) {
            return null;
        }

        return Service.builder()
                .id(ServiceId.of(serviceDO.getServiceId()))
                .name(serviceDO.getName())
                .description(serviceDO.getDescription())
                .version(serviceDO.getVersion())
                .type(ServiceType.valueOf(serviceDO.getType()))
                .endpoint(serviceDO.getEndpoint())
                .groupId(serviceDO.getGroupId())
                .artifactId(serviceDO.getArtifactId())
                .tags(serviceDO.getTags() != null ? serviceDO.getTags() : new ArrayList<>())
                .owner(serviceDO.getOwner())
                .apiDocUrl(serviceDO.getApiDocUrl())
                .status(ServiceStatus.valueOf(serviceDO.getStatus()))
                .metadata(serviceDO.getMetadata() != null ? serviceDO.getMetadata() : new HashMap<>())
                .createdAt(serviceDO.getCreatedAt() != null ? serviceDO.getCreatedAt() : LocalDateTime.now())
                .updatedAt(serviceDO.getUpdatedAt() != null ? serviceDO.getUpdatedAt() : LocalDateTime.now())
                .build();
    }

    /**
     * 批量转换数据对象为领域对象
     */
    public List<Service> toDomainObjects(List<ServiceDO> serviceDOs) {
        if (serviceDOs == null) {
            return new ArrayList<>();
        }

        List<Service> services = new ArrayList<>();
        for (ServiceDO serviceDO : serviceDOs) {
            Service service = toDomainObject(serviceDO);
            if (service != null) {
                services.add(service);
            }
        }
        return services;
    }

    /**
     * 批量转换领域对象为数据对象
     */
    public List<ServiceDO> toDataObjects(List<Service> services) {
        if (services == null) {
            return new ArrayList<>();
        }

        List<ServiceDO> serviceDOs = new ArrayList<>();
        for (Service service : services) {
            ServiceDO serviceDO = toDataObject(service);
            if (serviceDO != null) {
                serviceDOs.add(serviceDO);
            }
        }
        return serviceDOs;
    }
}