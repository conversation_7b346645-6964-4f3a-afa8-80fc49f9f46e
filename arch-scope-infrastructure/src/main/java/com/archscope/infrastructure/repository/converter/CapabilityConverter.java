package com.archscope.infrastructure.repository.converter;

import com.archscope.domain.model.servicediscovery.Capability;
import com.archscope.domain.valueobject.CapabilityId;
import com.archscope.domain.valueobject.ServiceId;
import com.archscope.infrastructure.persistence.entity.CapabilityDO;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 能力领域对象与数据对象转换器
 */
@Component
public class CapabilityConverter {

    /**
     * 将领域对象转换为数据对象
     */
    public CapabilityDO toDataObject(Capability capability) {
        if (capability == null) {
            return null;
        }

        CapabilityDO capabilityDO = new CapabilityDO();
        capabilityDO.setCapabilityId(capability.getId().getValue());
        capabilityDO.setServiceId(capability.getServiceId().getValue());
        capabilityDO.setName(capability.getName());
        capabilityDO.setDescription(capability.getDescription());
        capabilityDO.setFunctionSignature(capability.getFunctionSignature());
        capabilityDO.setInputSchema(new HashMap<>(capability.getInputSchema()));
        capabilityDO.setOutputSchema(new HashMap<>(capability.getOutputSchema()));
        capabilityDO.setExamples(new ArrayList<>(capability.getExamples()));
        capabilityDO.setTags(new ArrayList<>(capability.getTags()));
        capabilityDO.setCreatedAt(capability.getCreatedAt());
        capabilityDO.setUpdatedAt(capability.getUpdatedAt());

        return capabilityDO;
    }

    /**
     * 将数据对象转换为领域对象
     */
    public Capability toDomainObject(CapabilityDO capabilityDO) {
        if (capabilityDO == null) {
            return null;
        }

        return Capability.builder()
                .id(CapabilityId.of(capabilityDO.getCapabilityId()))
                .serviceId(ServiceId.of(capabilityDO.getServiceId()))
                .name(capabilityDO.getName())
                .description(capabilityDO.getDescription())
                .functionSignature(capabilityDO.getFunctionSignature())
                .inputSchema(capabilityDO.getInputSchema() != null ? capabilityDO.getInputSchema() : new HashMap<>())
                .outputSchema(capabilityDO.getOutputSchema() != null ? capabilityDO.getOutputSchema() : new HashMap<>())
                .examples(capabilityDO.getExamples() != null ? capabilityDO.getExamples() : new ArrayList<>())
                .tags(capabilityDO.getTags() != null ? capabilityDO.getTags() : new ArrayList<>())
                .createdAt(capabilityDO.getCreatedAt() != null ? capabilityDO.getCreatedAt() : LocalDateTime.now())
                .updatedAt(capabilityDO.getUpdatedAt() != null ? capabilityDO.getUpdatedAt() : LocalDateTime.now())
                .build();
    }

    /**
     * 批量转换数据对象为领域对象
     */
    public List<Capability> toDomainObjects(List<CapabilityDO> capabilityDOs) {
        if (capabilityDOs == null) {
            return new ArrayList<>();
        }

        List<Capability> capabilities = new ArrayList<>();
        for (CapabilityDO capabilityDO : capabilityDOs) {
            Capability capability = toDomainObject(capabilityDO);
            if (capability != null) {
                capabilities.add(capability);
            }
        }
        return capabilities;
    }

    /**
     * 批量转换领域对象为数据对象
     */
    public List<CapabilityDO> toDataObjects(List<Capability> capabilities) {
        if (capabilities == null) {
            return new ArrayList<>();
        }

        List<CapabilityDO> capabilityDOs = new ArrayList<>();
        for (Capability capability : capabilities) {
            CapabilityDO capabilityDO = toDataObject(capability);
            if (capabilityDO != null) {
                capabilityDOs.add(capabilityDO);
            }
        }
        return capabilityDOs;
    }
}