package com.archscope.infrastructure.repository;

import com.archscope.domain.entity.Project;
import com.archscope.domain.repository.ProjectRepository;
import com.archscope.domain.valueobject.ProjectType;
import com.archscope.infrastructure.cache.ProjectCacheService;
import com.archscope.infrastructure.mapper.ProjectMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 项目仓库MyBatis实现，集成缓存功能
 */
@Repository
@Primary
public class MyBatisProjectRepository implements ProjectRepository {

    private final ProjectMapper projectMapper;
    private final ProjectCacheService cacheService;

    public MyBatisProjectRepository(ProjectMapper projectMapper, ProjectCacheService cacheService) {
        System.out.println("🔍 MyBatisProjectRepository constructor called");
        this.projectMapper = projectMapper;
        this.cacheService = cacheService;
    }

    @Override
    public Project save(Project project) {
        if (project.getId() == null) {
            projectMapper.insert(project);
        } else {
            projectMapper.updateById(project);
        }
        // 更新缓存
        cacheService.cacheProject(project);
        // 清除列表缓存，因为列表已变更
        cacheService.clearAllProjectCache();
        return project;
    }

    @Override
    public Optional<Project> findById(Long id) {
        // 先尝试从缓存中获取
        Optional<Project> cachedProject = cacheService.getProjectById(id);

        if (cachedProject.isPresent()) {
            return cachedProject;
        }

        // 缓存未命中，从数据库获取
        Optional<Project> project = Optional.ofNullable(projectMapper.selectById(id));

        // 如果找到了，更新缓存
        project.ifPresent(cacheService::cacheProject);

        return project;
    }

    @Override
    public Optional<Project> findByRepositoryUrl(String repositoryUrl) {
        // 先尝试从缓存中获取
        Optional<Project> cachedProject = cacheService.getProjectByRepositoryUrl(repositoryUrl);

        if (cachedProject.isPresent()) {
            return cachedProject;
        }

        // 缓存未命中，从数据库获取
        LambdaQueryWrapper<Project> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Project::getRepositoryUrl, repositoryUrl);
        Optional<Project> project = Optional.ofNullable(projectMapper.selectOne(wrapper));

        // 如果找到了，更新缓存
        project.ifPresent(cacheService::cacheProject);

        return project;
    }

    @Override
    public Optional<Project> findByNormalizedRepositoryUrl(String normalizedRepositoryUrl) {
        // 直接查询数据库（可以考虑后续优化缓存策略）
        LambdaQueryWrapper<Project> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Project::getNormalizedRepositoryUrl, normalizedRepositoryUrl);
        Optional<Project> project = Optional.ofNullable(projectMapper.selectOne(wrapper));

        // 如果找到了，更新缓存
        project.ifPresent(cacheService::cacheProject);

        return project;
    }

    @Override
    public Optional<Project> findByName(String name) {
        // 直接查询数据库
        LambdaQueryWrapper<Project> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Project::getName, name);
        return Optional.ofNullable(projectMapper.selectOne(wrapper));
    }

    @Override
    public Project update(Project project) {
        projectMapper.updateById(project);
        // 更新缓存
        cacheService.removeProjectCache(project.getId()); // 移除旧缓存
        cacheService.cacheProject(project); // 缓存新数据
        cacheService.clearAllProjectCache(); // 清除列表缓存
        return project;
    }

    @Override
    public void delete(Long id) {
        projectMapper.deleteById(id);
        // 清除缓存
        cacheService.removeProjectCache(id);
        cacheService.clearAllProjectCache(); // 清除列表缓存
    }

    @Override
    public List<Project> findAll() {
        // 先尝试从缓存中获取
        Optional<List<Project>> cachedProjects = cacheService.getAllProjects();

        if (cachedProjects.isPresent()) {
            return cachedProjects.get();
        }

        // 缓存未命中，从数据库获取
        List<Project> projects = projectMapper.selectList(null);

        // 更新缓存
        if (!projects.isEmpty()) {
            cacheService.cacheProjectList(projects);
        }

        return projects;
    }

    @Override
    public List<Project> findAll(int page, int size) {
        System.out.println("🔍 MyBatisProjectRepository.findAll(page=" + page + ", size=" + size + ") called");
        Page<Project> projectPage = new Page<>(page + 1, size);
        LambdaQueryWrapper<Project> wrapper = new LambdaQueryWrapper<>();
        // 按ID降序排序，让最新的项目排在前面
        wrapper.orderByDesc(Project::getId);
        List<Project> result = projectMapper.selectPage(projectPage, wrapper).getRecords();
        System.out.println("🔍 MyBatisProjectRepository.findAll returned " + result.size() + " projects");
        if (!result.isEmpty()) {
            System.out.println("🔍 First project: " + result.get(0));
        }
        return result;
    }

    @Override
    public List<Project> findByType(ProjectType type) {
        LambdaQueryWrapper<Project> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Project::getType, type);
        return projectMapper.selectList(wrapper);
    }

    @Override
    public List<Project> findByUserId(Long userId) {
        return findByCreatorId(userId);
    }

    @Override
    public List<Project> findByCreatorId(Long creatorId) {
        LambdaQueryWrapper<Project> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Project::getCreatorId, creatorId);
        return projectMapper.selectList(wrapper);
    }

    @Override
    public List<Project> findRecentProjects(int limit) {
        LambdaQueryWrapper<Project> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByDesc(Project::getCreatedAt).last("LIMIT " + limit);
        return projectMapper.selectList(wrapper);
    }

    @Override
    public List<Project> findTopRatedProjects(int limit) {
        LambdaQueryWrapper<Project> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByDesc(Project::getRating).last("LIMIT " + limit);
        return projectMapper.selectList(wrapper);
    }

    @Override
    public long count() {
        return projectMapper.selectCount(null);
    }

    @Override
    public void deleteById(Long id) {
        projectMapper.deleteById(id);
    }

    @Override
    public boolean existsByRepositoryUrl(String repositoryUrl) {
        LambdaQueryWrapper<Project> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Project::getRepositoryUrl, repositoryUrl);
        return projectMapper.selectCount(wrapper) > 0;
    }

    @Override
    public boolean existsByNormalizedRepositoryUrl(String normalizedRepositoryUrl) {
        LambdaQueryWrapper<Project> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Project::getNormalizedRepositoryUrl, normalizedRepositoryUrl);
        return projectMapper.selectCount(wrapper) > 0;
    }
}