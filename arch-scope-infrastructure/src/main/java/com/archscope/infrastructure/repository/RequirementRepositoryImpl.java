package com.archscope.infrastructure.repository;

import com.archscope.domain.model.servicediscovery.Requirement;
import com.archscope.domain.model.servicediscovery.RequirementPriority;
import com.archscope.domain.repository.RequirementRepository;
import com.archscope.domain.valueobject.RequirementId;
import com.archscope.domain.valueobject.ServiceId;
import com.archscope.infrastructure.persistence.entity.RequirementCapabilityDO;
import com.archscope.infrastructure.persistence.entity.RequirementDO;
import com.archscope.infrastructure.persistence.mapper.RequirementCapabilityMapper;
import com.archscope.infrastructure.persistence.mapper.RequirementMapper;
import com.archscope.infrastructure.repository.converter.RequirementConverter;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 需求仓储实现类
 */
@Repository
public class RequirementRepositoryImpl implements RequirementRepository {

    private final RequirementMapper requirementMapper;
    private final RequirementCapabilityMapper requirementCapabilityMapper;
    private final RequirementConverter requirementConverter;

    public RequirementRepositoryImpl(RequirementMapper requirementMapper,
                                    RequirementCapabilityMapper requirementCapabilityMapper,
                                    RequirementConverter requirementConverter) {
        this.requirementMapper = requirementMapper;
        this.requirementCapabilityMapper = requirementCapabilityMapper;
        this.requirementConverter = requirementConverter;
    }

    @Override
    @Transactional
    public Requirement save(Requirement requirement) {
        RequirementDO requirementDO = requirementConverter.toDataObject(requirement);
        if (requirementMapper.selectById(requirementDO.getRequirementId()) != null) {
            requirementMapper.updateById(requirementDO);
        } else {
            requirementMapper.insert(requirementDO);
        }
        
        // 保存需求能力关联
        saveRequirementCapabilities(requirement);
        
        return requirementConverter.toDomainObject(requirementDO, requirement.getRequiredCapabilities());
    }

    @Override
    public Requirement findById(RequirementId id) {
        RequirementDO requirementDO = requirementMapper.selectById(id.getValue());
        if (requirementDO == null) {
            return null;
        }
        
        List<String> capabilities = requirementMapper.findCapabilitiesByRequirementId(id.getValue());
        return requirementConverter.toDomainObject(requirementDO, capabilities);
    }

    @Override
    public List<Requirement> findByPriority(RequirementPriority priority) {
        List<RequirementDO> requirementDOs = requirementMapper.findByPriority(priority.name());
        return requirementDOs.stream()
                .map(this::convertToDomainObjectWithCapabilities)
                .collect(Collectors.toList());
    }

    @Override
    public List<Requirement> findByRequiredCapability(String capabilityName) {
        List<String> requirementIds = requirementMapper.findRequirementIdsByCapabilityName(capabilityName);
        return requirementIds.stream()
                .map(id -> findById(RequirementId.of(id)))
                .collect(Collectors.toList());
    }

    @Override
    public List<Requirement> findAll() {
        List<RequirementDO> requirementDOs = requirementMapper.selectList(null);
        return requirementDOs.stream()
                .map(this::convertToDomainObjectWithCapabilities)
                .collect(Collectors.toList());
    }

    @Override
    public List<Requirement> findWithPagination(int offset, int limit) {
        List<RequirementDO> requirementDOs = requirementMapper.findWithPagination(offset, limit);
        return requirementDOs.stream()
                .map(this::convertToDomainObjectWithCapabilities)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public boolean delete(RequirementId id) {
        // 先删除需求能力关联
        requirementCapabilityMapper.deleteByRequirementId(id.getValue());
        
        // 再删除需求
        int deletedRows = requirementMapper.deleteById(id.getValue());
        return deletedRows > 0;
    }

    @Override
    public void recordMatchingFeedback(RequirementId requirementId, ServiceId serviceId, boolean satisfied, String feedback) {
        // 这里可以实现反馈记录逻辑
        // 可能需要创建一个新的表来存储反馈信息
        // 暂时留空，后续可以扩展
    }

    @Override
    public long count() {
        return requirementMapper.selectCount(null);
    }

    private void saveRequirementCapabilities(Requirement requirement) {
        String requirementId = requirement.getId().getValue();
        
        // 先删除现有的关联
        requirementCapabilityMapper.deleteByRequirementId(requirementId);
        
        // 保存新的关联
        for (String capabilityName : requirement.getRequiredCapabilities()) {
            RequirementCapabilityDO relationDO = new RequirementCapabilityDO();
            relationDO.setRequirementId(requirementId);
            relationDO.setCapabilityName(capabilityName);
            requirementCapabilityMapper.insert(relationDO);
        }
    }

    private Requirement convertToDomainObjectWithCapabilities(RequirementDO requirementDO) {
        List<String> capabilities = requirementMapper.findCapabilitiesByRequirementId(requirementDO.getRequirementId());
        return requirementConverter.toDomainObject(requirementDO, capabilities);
    }
}