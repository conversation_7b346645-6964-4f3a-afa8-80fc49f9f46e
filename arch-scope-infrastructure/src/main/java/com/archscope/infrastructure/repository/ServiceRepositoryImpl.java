package com.archscope.infrastructure.repository;

import com.archscope.domain.model.servicediscovery.Service;
import com.archscope.domain.model.servicediscovery.ServiceStatus;
import com.archscope.domain.model.servicediscovery.ServiceType;
import com.archscope.domain.repository.ServiceRepository;
import com.archscope.domain.repository.CapabilityRepository;
import com.archscope.domain.valueobject.ServiceId;
import com.archscope.infrastructure.cache.RedisService;
import com.archscope.infrastructure.persistence.entity.ServiceDO;
import com.archscope.infrastructure.persistence.mapper.ServiceMapper;
import com.archscope.infrastructure.repository.converter.ServiceConverter;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 服务仓储实现类 - 带性能优化
 */
@Repository
public class ServiceRepositoryImpl implements ServiceRepository {

    private static final String CACHE_PREFIX_SERVICE = "service:";
    private static final String CACHE_PREFIX_SERVICES_BY_TYPE = "services:type:";
    private static final String CACHE_PREFIX_SERVICES_BY_STATUS = "services:status:";
    private static final String CACHE_PREFIX_SERVICES_BY_TAG = "services:tag:";
    private static final String CACHE_PREFIX_SERVICES_BY_MAVEN = "services:maven:";
    private static final int CACHE_EXPIRE_MINUTES = 30;

    private final ServiceMapper serviceMapper;
    private final ServiceConverter serviceConverter;
    private final RedisService redisService;
    private final CapabilityRepository capabilityRepository;

    public ServiceRepositoryImpl(ServiceMapper serviceMapper,
                                ServiceConverter serviceConverter,
                                RedisService redisService,
                                CapabilityRepository capabilityRepository) {
        this.serviceMapper = serviceMapper;
        this.serviceConverter = serviceConverter;
        this.redisService = redisService;
        this.capabilityRepository = capabilityRepository;
    }

    @Override
    @Transactional
    public Service save(Service service) {
        ServiceDO serviceDO = serviceConverter.toDataObject(service);
        
        if (serviceMapper.selectById(serviceDO.getServiceId()) != null) {
            serviceMapper.updateById(serviceDO);
        } else {
            serviceMapper.insert(serviceDO);
        }
        
        // 清除相关缓存
        clearServiceCaches(service.getId().getValue());
        
        return serviceConverter.toDomainObject(serviceDO);
    }

    @Override
    public Service findById(ServiceId id) {
        String cacheKey = CACHE_PREFIX_SERVICE + id.getValue();
        
        // 尝试从缓存获取
        Service cachedService = (Service) redisService.get(cacheKey);
        if (cachedService != null) {
            return cachedService;
        }
        
        // 从数据库查询
        ServiceDO serviceDO = serviceMapper.selectById(id.getValue());
        if (serviceDO == null) {
            return null;
        }
        
        Service service = serviceConverter.toDomainObject(serviceDO);
        
        // 缓存结果
        redisService.set(cacheKey, service, CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES);
        
        return service;
    }

    @Override
    public Service findByName(String name) {
        ServiceDO serviceDO = serviceMapper.findByName(name);
        return serviceDO != null ? serviceConverter.toDomainObject(serviceDO) : null;
    }

    @Override
    public Service findByEndpoint(java.net.URL endpoint) {
        ServiceDO serviceDO = serviceMapper.findByEndpoint(endpoint.toString());
        return serviceDO != null ? serviceConverter.toDomainObject(serviceDO) : null;
    }

    @Override
    public Service findByMavenCoordinates(String groupId, String artifactId, String version) {
        String cacheKey = CACHE_PREFIX_SERVICES_BY_MAVEN + groupId + ":" + artifactId + ":" + version;
        
        // 尝试从缓存获取
        Service cachedService = (Service) redisService.get(cacheKey);
        if (cachedService != null) {
            return cachedService;
        }
        
        ServiceDO serviceDO = serviceMapper.findByMavenCoordinates(groupId, artifactId, version);
        if (serviceDO == null) {
            return null;
        }
        
        Service service = serviceConverter.toDomainObject(serviceDO);
        
        // 缓存结果
        redisService.set(cacheKey, service, CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES);
        
        return service;
    }

    @Override
    public List<Service> findByMavenCoordinatesWithoutVersion(String groupId, String artifactId) {
        String cacheKey = CACHE_PREFIX_SERVICES_BY_MAVEN + groupId + ":" + artifactId;
        
        // 尝试从缓存获取
        @SuppressWarnings("unchecked")
        List<Service> cachedServices = redisService.getList(cacheKey, Service.class);
        if (cachedServices != null) {
            return cachedServices;
        }
        
        List<ServiceDO> serviceDOs = serviceMapper.findByMavenCoordinatesWithoutVersion(groupId, artifactId);
        List<Service> services = serviceDOs.stream()
                .map(serviceConverter::toDomainObject)
                .collect(Collectors.toList());
        
        // 缓存结果
        redisService.setList(cacheKey, services, CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES);
        
        return services;
    }

    @Override
    public List<Service> findByStatus(ServiceStatus status) {
        String cacheKey = CACHE_PREFIX_SERVICES_BY_STATUS + status.name();
        
        // 尝试从缓存获取
        @SuppressWarnings("unchecked")
        List<Service> cachedServices = redisService.getList(cacheKey, Service.class);
        if (cachedServices != null) {
            return cachedServices;
        }
        
        List<ServiceDO> serviceDOs = serviceMapper.findByStatus(status.name());
        List<Service> services = serviceDOs.stream()
                .map(serviceConverter::toDomainObject)
                .collect(Collectors.toList());
        
        // 缓存结果
        redisService.setList(cacheKey, services, CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES);
        
        return services;
    }

    @Override
    public List<Service> findByType(ServiceType type) {
        String cacheKey = CACHE_PREFIX_SERVICES_BY_TYPE + type.name();
        
        // 尝试从缓存获取
        @SuppressWarnings("unchecked")
        List<Service> cachedServices = redisService.getList(cacheKey, Service.class);
        if (cachedServices != null) {
            return cachedServices;
        }
        
        List<ServiceDO> serviceDOs = serviceMapper.findByType(type.name());
        List<Service> services = serviceDOs.stream()
                .map(serviceConverter::toDomainObject)
                .collect(Collectors.toList());
        
        // 缓存结果
        redisService.setList(cacheKey, services, CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES);
        
        return services;
    }

    @Override
    public List<Service> findByTags(java.util.Set<com.archscope.domain.valueobject.Tag> tags) {
        List<String> tagStrings = tags.stream()
                .map(com.archscope.domain.valueobject.Tag::getValue)
                .collect(Collectors.toList());
        
        String cacheKey = CACHE_PREFIX_SERVICES_BY_TAG + String.join(",", tagStrings);
        
        // 尝试从缓存获取
        @SuppressWarnings("unchecked")
        List<Service> cachedServices = redisService.getList(cacheKey, Service.class);
        if (cachedServices != null) {
            return cachedServices;
        }
        
        List<ServiceDO> serviceDOs = serviceMapper.findByMultipleTags(tagStrings);
        List<Service> services = serviceDOs.stream()
                .map(serviceConverter::toDomainObject)
                .collect(Collectors.toList());
        
        // 缓存结果
        redisService.setList(cacheKey, services, CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES);
        
        return services;
    }

    @Override
    public List<Service> findByOwner(String owner) {
        List<ServiceDO> serviceDOs = serviceMapper.findByOwner(owner);
        return serviceDOs.stream()
                .map(serviceConverter::toDomainObject)
                .collect(Collectors.toList());
    }

    @Override
    public List<Service> findWithPagination(int offset, int limit) {
        List<ServiceDO> serviceDOs = serviceMapper.findWithPagination(offset, limit);
        return serviceDOs.stream()
                .map(serviceConverter::toDomainObject)
                .collect(Collectors.toList());
    }

    @Override
    public List<Service> findAll() {
        List<ServiceDO> serviceDOs = serviceMapper.selectList(null);
        return serviceDOs.stream()
                .map(serviceConverter::toDomainObject)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public boolean delete(ServiceId id) {
        int deletedRows = serviceMapper.deleteById(id.getValue());
        if (deletedRows > 0) {
            clearServiceCaches(id.getValue());
            return true;
        }
        return false;
    }

    @Override
    public long count() {
        return serviceMapper.selectCount(null);
    }

    @Override
    public List<Service> findRecentlyRegistered(int limit) {
        List<ServiceDO> serviceDOs = serviceMapper.findRecentlyRegistered(limit);
        return serviceDOs.stream()
                .map(serviceConverter::toDomainObject)
                .collect(Collectors.toList());
    }

    @Override
    public List<Service> findRecentlyUpdated(int limit) {
        List<ServiceDO> serviceDOs = serviceMapper.findRecentlyUpdated(limit);
        return serviceDOs.stream()
                .map(serviceConverter::toDomainObject)
                .collect(Collectors.toList());
    }

    @Override
    public List<Service> searchByName(String nameLike) {
        List<ServiceDO> serviceDOs = serviceMapper.findByNameLike(nameLike);
        return serviceDOs.stream()
                .map(serviceConverter::toDomainObject)
                .collect(Collectors.toList());
    }

    @Override
    public List<Service> searchByDescription(String descriptionLike) {
        List<ServiceDO> serviceDOs = serviceMapper.findByDescriptionLike(descriptionLike);
        return serviceDOs.stream()
                .map(serviceConverter::toDomainObject)
                .collect(Collectors.toList());
    }

    /**
     * 批量保存服务
     */
    @Transactional
    public List<Service> saveBatch(List<Service> services) {
        List<ServiceDO> serviceDOs = services.stream()
                .map(serviceConverter::toDataObject)
                .collect(Collectors.toList());
        
        // 使用MyBatis Plus的批量插入
        for (ServiceDO serviceDO : serviceDOs) {
            if (serviceMapper.selectById(serviceDO.getServiceId()) != null) {
                serviceMapper.updateById(serviceDO);
            } else {
                serviceMapper.insert(serviceDO);
            }
        }
        
        // 清除相关缓存
        services.forEach(service -> clearServiceCaches(service.getId().getValue()));
        
        return services.stream()
                .map(service -> serviceConverter.toDomainObject(serviceConverter.toDataObject(service)))
                .collect(Collectors.toList());
    }

    /**
     * 批量删除服务
     */
    @Transactional
    public boolean deleteBatch(List<ServiceId> serviceIds) {
        List<String> ids = serviceIds.stream()
                .map(ServiceId::getValue)
                .collect(Collectors.toList());
        
        int deletedRows = serviceMapper.deleteBatchIds(ids);
        
        if (deletedRows > 0) {
            ids.forEach(this::clearServiceCaches);
            return true;
        }
        return false;
    }

    /**
     * 优化的复合查询方法
     */
    public List<Service> findByMultipleCriteria(String name, String type, String status, 
                                               String groupId, String artifactId, String version,
                                               String owner, String tag, String orderBy, 
                                               int offset, int limit) {
        // 构建缓存键
        String cacheKey = buildComplexQueryCacheKey(name, type, status, groupId, artifactId, 
                                                   version, owner, tag, orderBy, offset, limit);
        
        // 尝试从缓存获取
        @SuppressWarnings("unchecked")
        List<Service> cachedServices = redisService.getList(cacheKey, Service.class);
        if (cachedServices != null) {
            return cachedServices;
        }
        
        // 使用XML中定义的复杂查询
        QueryWrapper<ServiceDO> queryWrapper = new QueryWrapper<>();
        
        if (name != null && !name.isEmpty()) {
            queryWrapper.like("name", name);
        }
        if (type != null && !type.isEmpty()) {
            queryWrapper.eq("type", type);
        }
        if (status != null && !status.isEmpty()) {
            queryWrapper.eq("status", status);
        }
        if (groupId != null && !groupId.isEmpty()) {
            queryWrapper.eq("group_id", groupId);
        }
        if (artifactId != null && !artifactId.isEmpty()) {
            queryWrapper.eq("artifact_id", artifactId);
        }
        if (version != null && !version.isEmpty()) {
            queryWrapper.eq("version", version);
        }
        if (owner != null && !owner.isEmpty()) {
            queryWrapper.eq("owner", owner);
        }
        
        // 排序
        if (orderBy != null && !orderBy.isEmpty()) {
            queryWrapper.orderByDesc(orderBy);
        } else {
            queryWrapper.orderByDesc("created_at");
        }
        
        // 分页
        Page<ServiceDO> page = new Page<>(offset / limit + 1, limit);
        Page<ServiceDO> result = serviceMapper.selectPage(page, queryWrapper);
        
        List<Service> services = result.getRecords().stream()
                .map(serviceConverter::toDomainObject)
                .collect(Collectors.toList());
        
        // 缓存结果（较短的过期时间，因为查询条件复杂）
        redisService.setList(cacheKey, services, 10, TimeUnit.MINUTES);
        
        return services;
    }

    private void clearServiceCaches(String serviceId) {
        // 清除单个服务缓存
        redisService.delete(CACHE_PREFIX_SERVICE + serviceId);
        
        // 清除相关的列表缓存
        redisService.deleteByPattern(CACHE_PREFIX_SERVICES_BY_TYPE + "*");
        redisService.deleteByPattern(CACHE_PREFIX_SERVICES_BY_STATUS + "*");
        redisService.deleteByPattern(CACHE_PREFIX_SERVICES_BY_TAG + "*");
        redisService.deleteByPattern(CACHE_PREFIX_SERVICES_BY_MAVEN + "*");
    }

    // Additional methods to match domain interface

    @Override
    public Service findById(String id) {
        return findById(ServiceId.of(id));
    }

    @Override
    public List<Service> findByNameContainingIgnoreCase(String name) {
        return searchByName(name);
    }

    @Override
    public List<Service> findByStatus(String status) {
        return findByStatus(ServiceStatus.valueOf(status));
    }

    @Override
    public List<Service> findByType(String type) {
        return findByType(ServiceType.valueOf(type));
    }

    @Override
    public List<Service> findByTagsIn(List<String> tags) {
        java.util.Set<com.archscope.domain.valueobject.Tag> tagSet = tags.stream()
                .map(com.archscope.domain.valueobject.Tag::of)
                .collect(Collectors.toSet());
        return findByTags(tagSet);
    }

    @Override
    public List<Service> findByCapabilityName(String capabilityName) {
        // This would need to join with capabilities table
        List<ServiceId> serviceIds = capabilityRepository.findServiceIdsByCapabilityName(capabilityName);
        return serviceIds.stream()
                .map(this::findById)
                .filter(service -> service != null)
                .collect(Collectors.toList());
    }

    @Override
    public List<Service> findByGroupIdAndArtifactId(String groupId, String artifactId) {
        return findByMavenCoordinatesWithoutVersion(groupId, artifactId);
    }

    @Override
    public Service findByGroupIdAndArtifactIdAndVersion(String groupId, String artifactId, String version) {
        return findByMavenCoordinates(groupId, artifactId, version);
    }

    @Override
    public com.archscope.domain.valueobject.PageResult<Service> findAll(com.archscope.domain.valueobject.PageRequest pageRequest) {
        int offset = (pageRequest.getPage() - 1) * pageRequest.getSize();
        List<Service> services = findWithPagination(offset, pageRequest.getSize());
        long total = count();
        return new com.archscope.domain.valueobject.PageResult<>(services, pageRequest.getPage(), pageRequest.getSize(), total);
    }

    @Override
    public com.archscope.domain.valueobject.PageResult<Service> findByCriteria(String name, String type, String status, 
                                                                               List<String> tags, String groupId, 
                                                                               String artifactId, String capabilityName, 
                                                                               com.archscope.domain.valueobject.PageRequest pageRequest) {
        int offset = (pageRequest.getPage() - 1) * pageRequest.getSize();
        List<Service> services = findByMultipleCriteria(name, type, status, groupId, artifactId, null, null, 
                                                       tags != null && !tags.isEmpty() ? tags.get(0) : null, 
                                                       null, offset, pageRequest.getSize());
        
        // For capability filtering, we need additional logic
        if (capabilityName != null && !capabilityName.isEmpty()) {
            List<ServiceId> serviceIds = capabilityRepository.findServiceIdsByCapabilityName(capabilityName);
            services = services.stream()
                    .filter(service -> serviceIds.contains(service.getId()))
                    .collect(Collectors.toList());
        }
        
        long total = services.size(); // This is approximate, should be calculated properly
        return new com.archscope.domain.valueobject.PageResult<>(services, pageRequest.getPage(), pageRequest.getSize(), total);
    }

    @Override
    public long countByStatus(String status) {
        return findByStatus(status).size();
    }

    @Override
    public java.util.Map<String, Long> countByStatusGrouped() {
        List<java.util.Map<String, Object>> stats = serviceMapper.countByStatus();
        return stats.stream()
                .collect(Collectors.toMap(
                        map -> (String) map.get("status"),
                        map -> ((Number) map.get("count")).longValue()
                ));
    }

    @Override
    public java.util.Map<String, Long> countByTypeGrouped() {
        List<java.util.Map<String, Object>> stats = serviceMapper.countByType();
        return stats.stream()
                .collect(Collectors.toMap(
                        map -> (String) map.get("type"),
                        map -> ((Number) map.get("count")).longValue()
                ));
    }

    @Override
    public long countRecentServices(int days) {
        // This would need a specific query, for now return approximate
        return findRecentlyRegistered(100).size();
    }

    private String buildComplexQueryCacheKey(String name, String type, String status,
                                           String groupId, String artifactId, String version,
                                           String owner, String tag, String orderBy,
                                           int offset, int limit) {
        return "services:complex:" + 
               (name != null ? name : "") + ":" +
               (type != null ? type : "") + ":" +
               (status != null ? status : "") + ":" +
               (groupId != null ? groupId : "") + ":" +
               (artifactId != null ? artifactId : "") + ":" +
               (version != null ? version : "") + ":" +
               (owner != null ? owner : "") + ":" +
               (tag != null ? tag : "") + ":" +
               (orderBy != null ? orderBy : "") + ":" +
               offset + ":" + limit;
    }
}