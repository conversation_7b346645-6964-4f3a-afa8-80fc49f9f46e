package com.archscope.infrastructure.repository.converter;

import com.archscope.domain.model.servicediscovery.Requirement;
import com.archscope.domain.model.servicediscovery.RequirementPriority;
import com.archscope.domain.valueobject.RequirementId;
import com.archscope.infrastructure.persistence.entity.RequirementDO;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;

/**
 * 需求转换器，负责在需求领域对象和数据对象之间进行转换
 */
@Component
public class RequirementConverter {

    /**
     * 将需求领域对象转换为数据对象
     *
     * @param requirement 需求领域对象
     * @return 需求数据对象
     */
    public RequirementDO toDataObject(Requirement requirement) {
        if (requirement == null) {
            return null;
        }

        RequirementDO requirementDO = new RequirementDO();
        requirementDO.setRequirementId(requirement.getId().getValue());
        requirementDO.setTitle(extractTitleFromDescription(requirement.getDescription()));
        requirementDO.setDescription(requirement.getDescription());
        requirementDO.setPriority(requirement.getPriority().name());
        requirementDO.setStatus("NEW"); // 默认状态
        requirementDO.setCreatedAt(instantToLocalDateTime(requirement.getCreatedAt()));
        requirementDO.setUpdatedAt(instantToLocalDateTime(requirement.getLastUpdatedAt()));

        return requirementDO;
    }

    /**
     * 将需求数据对象转换为领域对象
     *
     * @param requirementDO 需求数据对象
     * @param requiredCapabilities 需求的能力列表
     * @return 需求领域对象
     */
    public Requirement toDomainObject(RequirementDO requirementDO, List<String> requiredCapabilities) {
        if (requirementDO == null) {
            return null;
        }

        return Requirement.restore(
                RequirementId.of(requirementDO.getRequirementId()),
                requirementDO.getDescription(),
                requiredCapabilities,
                RequirementPriority.valueOf(requirementDO.getPriority()),
                localDateTimeToInstant(requirementDO.getCreatedAt()),
                localDateTimeToInstant(requirementDO.getUpdatedAt())
        );
    }

    private String extractTitleFromDescription(String description) {
        if (description == null || description.trim().isEmpty()) {
            return "Untitled Requirement";
        }
        
        // 取描述的前50个字符作为标题
        String title = description.length() > 50 ? description.substring(0, 50) + "..." : description;
        return title.replaceAll("\\n", " ").trim();
    }

    private LocalDateTime instantToLocalDateTime(Instant instant) {
        return instant != null ? LocalDateTime.ofInstant(instant, ZoneId.systemDefault()) : null;
    }

    private Instant localDateTimeToInstant(LocalDateTime localDateTime) {
        return localDateTime != null ? localDateTime.atZone(ZoneId.systemDefault()).toInstant() : null;
    }
}