package com.archscope.infrastructure.exception;

/**
 * 数据库访问异常
 */
public class DatabaseAccessException extends ServiceDiscoveryInfrastructureException {

    public DatabaseAccessException(String operation) {
        super("Database access failed during operation: " + operation);
    }

    public DatabaseAccessException(String operation, Throwable cause) {
        super("Database access failed during operation: " + operation, cause);
    }

    public DatabaseAccessException(String message, Throwable cause) {
        super(message, cause);
    }
}