package com.archscope.infrastructure.messaging.consumer;

import com.archscope.domain.constants.MessageConstants;
import com.archscope.domain.entity.Task;
import com.archscope.domain.message.TaskMessage;
import com.archscope.domain.service.TaskQueueService;
import com.archscope.domain.task.ProjectAnalysisTask;
import com.archscope.domain.task.TaskExecutor;
import com.archscope.domain.task.TaskExecutorRegistry;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * 项目全量分析任务消息消费者
 * 负责处理项目全量分析任务的消息队列消费
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(name = "rocketmq.consumer.enabled", havingValue = "true", matchIfMissing = true)
@RocketMQMessageListener(
        topic = MessageConstants.TOPIC_PROJECT_ANALYSIS,
        consumerGroup = MessageConstants.GROUP_PROJECT_ANALYSIS,
        consumeMode = ConsumeMode.CONCURRENTLY,
        messageModel = MessageModel.CLUSTERING
)
public class ProjectAnalysisTaskConsumer implements RocketMQListener<TaskMessage> {

    private final TaskQueueService taskQueueService;
    private final TaskExecutorRegistry taskExecutorRegistry;

    @Override
    public void onMessage(TaskMessage message) {
        log.info("接收到项目全量分析任务消息: {}", message);
        
        try {
            // 获取任务ID
            Long taskId = message.getTaskId();
            if (taskId == null) {
                log.error("任务ID为空，无法处理任务消息");
                return;
            }
            
            // 获取任务详情
            Optional<Task> taskOpt = taskQueueService.getTaskById(taskId);
            if (!taskOpt.isPresent()) {
                log.error("任务不存在: {}", taskId);
                return;
            }
            
            Task task = taskOpt.get();
            
            // 验证任务类型
            if (!ProjectAnalysisTask.TASK_TYPE.equals(task.getTaskType())) {
                log.error("任务类型不匹配: 期望={}, 实际={}", ProjectAnalysisTask.TASK_TYPE, task.getTaskType());
                taskQueueService.recordTaskError(taskId, "任务类型不匹配");
                return;
            }
            
            // 获取任务执行器
            TaskExecutor executor = taskExecutorRegistry.getExecutor(ProjectAnalysisTask.TASK_TYPE);
            if (executor == null) {
                log.error("未找到项目全量分析任务执行器");
                taskQueueService.recordTaskError(taskId, "未找到项目全量分析任务执行器");
                return;
            }
            
            // 执行任务
            log.info("开始执行项目全量分析任务: {}", taskId);
            executor.execute(task);
            
        } catch (Exception e) {
            log.error("处理项目全量分析任务消息异常", e);
            
            // 记录错误到任务
            if (message.getTaskId() != null) {
                try {
                    taskQueueService.recordTaskError(message.getTaskId(), "消息处理异常: " + e.getMessage());
                } catch (Exception recordError) {
                    log.error("记录任务错误失败: taskId={}", message.getTaskId(), recordError);
                }
            }
        }
    }
    
    /**
     * 检查是否可以处理指定的任务消息
     * 
     * @param message 任务消息
     * @return 是否可以处理
     */
    public boolean canHandle(TaskMessage message) {
        return message != null 
            && message.getTaskType() != null 
            && ProjectAnalysisTask.TASK_TYPE.equals(message.getTaskType());
    }
}
