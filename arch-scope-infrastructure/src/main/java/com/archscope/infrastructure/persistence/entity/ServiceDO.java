package com.archscope.infrastructure.persistence.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 服务数据对象，对应数据库中的services表
 */
@Data
@TableName(value = "services", autoResultMap = true)
public class ServiceDO {

    /**
     * 服务唯一标识符 (UUID)
     */
    @TableId("service_id")
    private String serviceId;

    /**
     * 服务名称
     */
    private String name;

    /**
     * 服务描述
     */
    private String description;

    /**
     * 服务版本
     */
    private String version;

    /**
     * 服务类型
     */
    private String type;

    /**
     * 服务端点URL
     */
    private String endpoint;

    /**
     * Maven/Gradle坐标 - groupId
     */
    private String groupId;

    /**
     * Maven/Gradle坐标 - artifactId
     */
    private String artifactId;

    /**
     * 服务标签 (JSON数组)
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> tags;

    /**
     * 服务所有者
     */
    private String owner;

    /**
     * API文档URL
     */
    private String apiDocUrl;

    /**
     * 服务状态: ACTIVE, INACTIVE, DEPRECATED, MAINTENANCE, UNKNOWN
     */
    private String status;

    /**
     * 元数据 (JSON对象)
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> metadata;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}