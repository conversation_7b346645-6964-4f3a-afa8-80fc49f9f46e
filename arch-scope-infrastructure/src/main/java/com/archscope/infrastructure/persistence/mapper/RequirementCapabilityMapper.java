package com.archscope.infrastructure.persistence.mapper;

import com.archscope.infrastructure.persistence.entity.RequirementCapabilityDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 需求能力关联Mapper接口，定义需求能力关联数据对象的数据库操作
 */
@Mapper
public interface RequirementCapabilityMapper extends BaseMapper<RequirementCapabilityDO> {

    /**
     * 根据需求ID查找需求能力关联列表
     *
     * @param requirementId 需求ID
     * @return 找到的需求能力关联数据对象列表
     */
    @Select("SELECT * FROM requirement_capabilities WHERE requirement_id = #{requirementId}")
    List<RequirementCapabilityDO> findByRequirementId(@Param("requirementId") String requirementId);

    /**
     * 根据能力名称查找需求能力关联列表
     *
     * @param capabilityName 能力名称
     * @return 找到的需求能力关联数据对象列表
     */
    @Select("SELECT * FROM requirement_capabilities WHERE capability_name = #{capabilityName}")
    List<RequirementCapabilityDO> findByCapabilityName(@Param("capabilityName") String capabilityName);

    /**
     * 删除需求的所有能力关联
     *
     * @param requirementId 需求ID
     * @return 删除的记录数
     */
    @Select("DELETE FROM requirement_capabilities WHERE requirement_id = #{requirementId}")
    int deleteByRequirementId(@Param("requirementId") String requirementId);
}