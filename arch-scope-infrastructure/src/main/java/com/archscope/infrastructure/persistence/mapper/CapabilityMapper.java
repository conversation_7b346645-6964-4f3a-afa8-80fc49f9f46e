package com.archscope.infrastructure.persistence.mapper;

import com.archscope.infrastructure.persistence.entity.CapabilityDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Delete;

import java.util.List;

/**
 * 能力Mapper接口，定义能力数据对象的数据库操作
 */
@Mapper
public interface CapabilityMapper extends BaseMapper<CapabilityDO> {

    /**
     * 根据能力ID查找能力
     *
     * @param capabilityId 能力ID
     * @return 找到的能力数据对象
     */
    @Select("SELECT * FROM capabilities WHERE capability_id = #{capabilityId} LIMIT 1")
    CapabilityDO findByCapabilityId(@Param("capabilityId") String capabilityId);

    /**
     * 根据服务ID和能力名称查找能力
     *
     * @param serviceId 服务ID
     * @param name 能力名称
     * @return 找到的能力数据对象
     */
    @Select("SELECT * FROM capabilities WHERE service_id = #{serviceId} AND name = #{name} LIMIT 1")
    CapabilityDO findByServiceIdAndName(@Param("serviceId") String serviceId, @Param("name") String name);

    /**
     * 根据服务ID查找能力列表
     *
     * @param serviceId 服务ID
     * @return 找到的能力数据对象列表
     */
    @Select("SELECT * FROM capabilities WHERE service_id = #{serviceId}")
    List<CapabilityDO> findByServiceId(@Param("serviceId") String serviceId);

    /**
     * 根据名称查找能力列表
     *
     * @param name 能力名称
     * @return 找到的能力数据对象列表
     */
    @Select("SELECT * FROM capabilities WHERE name = #{name}")
    List<CapabilityDO> findByName(@Param("name") String name);

    /**
     * 查找所有能力名称
     *
     * @return 所有能力名称列表
     */
    @Select("SELECT DISTINCT name FROM capabilities")
    List<String> findAllCapabilityNames();

    /**
     * 根据能力名称查找提供该能力的服务ID列表
     *
     * @param capabilityName 能力名称
     * @return 提供该能力的服务ID列表
     */
    @Select("SELECT service_id FROM capabilities WHERE name = #{capabilityName}")
    List<String> findServiceIdsByCapabilityName(@Param("capabilityName") String capabilityName);

    /**
     * 删除服务的所有能力
     *
     * @param serviceId 服务ID
     * @return 删除的记录数
     */
    @Delete("DELETE FROM capabilities WHERE service_id = #{serviceId}")
    int deleteByServiceId(@Param("serviceId") String serviceId);
    
    /**
     * 根据标签查找能力列表
     *
     * @param tag 标签
     * @return 找到的能力数据对象列表
     */
    @Select("SELECT * FROM capabilities WHERE JSON_CONTAINS(tags, JSON_ARRAY(#{tag}))")
    List<CapabilityDO> findByTag(@Param("tag") String tag);
    
    /**
     * 根据描述模糊查询能力
     *
     * @param descriptionLike 描述模糊匹配
     * @return 找到的能力数据对象列表
     */
    @Select("SELECT * FROM capabilities WHERE description LIKE CONCAT('%', #{descriptionLike}, '%')")
    List<CapabilityDO> findByDescriptionLike(@Param("descriptionLike") String descriptionLike);
    
    /**
     * 查找最近添加的能力
     *
     * @param limit 限制数量
     * @return 最近添加的能力数据对象列表
     */
    @Select("SELECT * FROM capabilities ORDER BY created_at DESC LIMIT #{limit}")
    List<CapabilityDO> findRecentlyAdded(@Param("limit") int limit);
    
    /**
     * 查找最近更新的能力
     *
     * @param limit 限制数量
     * @return 最近更新的能力数据对象列表
     */
    @Select("SELECT * FROM capabilities ORDER BY updated_at DESC LIMIT #{limit}")
    List<CapabilityDO> findRecentlyUpdated(@Param("limit") int limit);
    
    /**
     * 分页查询能力
     *
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 分页的能力数据对象列表
     */
    @Select("SELECT * FROM capabilities LIMIT #{limit} OFFSET #{offset}")
    List<CapabilityDO> findWithPagination(@Param("offset") int offset, @Param("limit") int limit);
    
    /**
     * 根据多个服务ID查找能力列表
     *
     * @param serviceIds 服务ID列表
     * @return 找到的能力数据对象列表
     */
    @Select("<script>SELECT * FROM capabilities WHERE service_id IN " +
            "<foreach collection='serviceIds' item='id' open='(' separator=',' close=')'>#{id}</foreach>" +
            "</script>")
    List<CapabilityDO> findByServiceIds(@Param("serviceIds") List<String> serviceIds);
}