package com.archscope.infrastructure.persistence.mapper;

import com.archscope.infrastructure.persistence.entity.ServiceDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 服务Mapper接口，定义服务数据对象的数据库操作
 */
@Mapper
public interface ServiceMapper extends BaseMapper<ServiceDO> {

    /**
     * 根据服务ID查找服务
     *
     * @param serviceId 服务ID
     * @return 找到的服务数据对象
     */
    @Select("SELECT * FROM services WHERE service_id = #{serviceId} LIMIT 1")
    ServiceDO findByServiceId(@Param("serviceId") String serviceId);

    /**
     * 根据名称查找服务
     *
     * @param name 服务名称
     * @return 找到的服务数据对象
     */
    @Select("SELECT * FROM services WHERE name = #{name} LIMIT 1")
    ServiceDO findByName(@Param("name") String name);

    /**
     * 根据端点URL查找服务
     *
     * @param endpoint 服务端点URL
     * @return 找到的服务数据对象
     */
    @Select("SELECT * FROM services WHERE endpoint = #{endpoint} LIMIT 1")
    ServiceDO findByEndpoint(@Param("endpoint") String endpoint);

    /**
     * 根据Maven坐标查找服务
     *
     * @param groupId Maven/Gradle坐标 - groupId
     * @param artifactId Maven/Gradle坐标 - artifactId
     * @param version 服务版本
     * @return 找到的服务数据对象
     */
    @Select("SELECT * FROM services WHERE group_id = #{groupId} AND artifact_id = #{artifactId} AND version = #{version} LIMIT 1")
    ServiceDO findByMavenCoordinates(@Param("groupId") String groupId, @Param("artifactId") String artifactId, @Param("version") String version);

    /**
     * 根据Maven坐标查找服务列表（不指定版本）
     *
     * @param groupId Maven/Gradle坐标 - groupId
     * @param artifactId Maven/Gradle坐标 - artifactId
     * @return 找到的服务数据对象列表
     */
    @Select("SELECT * FROM services WHERE group_id = #{groupId} AND artifact_id = #{artifactId}")
    List<ServiceDO> findByMavenCoordinatesWithoutVersion(@Param("groupId") String groupId, @Param("artifactId") String artifactId);

    /**
     * 根据状态查找服务列表
     *
     * @param status 服务状态
     * @return 找到的服务数据对象列表
     */
    @Select("SELECT * FROM services WHERE status = #{status}")
    List<ServiceDO> findByStatus(@Param("status") String status);

    /**
     * 根据类型查找服务列表
     *
     * @param type 服务类型
     * @return 找到的服务数据对象列表
     */
    @Select("SELECT * FROM services WHERE type = #{type}")
    List<ServiceDO> findByType(@Param("type") String type);

    /**
     * 分页查询服务
     *
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 分页的服务数据对象列表
     */
    @Select("SELECT * FROM services LIMIT #{limit} OFFSET #{offset}")
    List<ServiceDO> findWithPagination(@Param("offset") int offset, @Param("limit") int limit);
    
    /**
     * 根据标签查找服务列表
     *
     * @param tag 标签
     * @return 找到的服务数据对象列表
     */
    @Select("SELECT * FROM services WHERE JSON_CONTAINS(tags, JSON_ARRAY(#{tag}))")
    List<ServiceDO> findByTag(@Param("tag") String tag);
    
    /**
     * 根据所有者查找服务列表
     *
     * @param owner 所有者
     * @return 找到的服务数据对象列表
     */
    @Select("SELECT * FROM services WHERE owner = #{owner}")
    List<ServiceDO> findByOwner(@Param("owner") String owner);
    
    /**
     * 查找最近注册的服务
     *
     * @param limit 限制数量
     * @return 最近注册的服务数据对象列表
     */
    @Select("SELECT * FROM services ORDER BY created_at DESC LIMIT #{limit}")
    List<ServiceDO> findRecentlyRegistered(@Param("limit") int limit);
    
    /**
     * 查找最近更新的服务
     *
     * @param limit 限制数量
     * @return 最近更新的服务数据对象列表
     */
    @Select("SELECT * FROM services ORDER BY updated_at DESC LIMIT #{limit}")
    List<ServiceDO> findRecentlyUpdated(@Param("limit") int limit);
    
    /**
     * 根据名称模糊查询服务
     *
     * @param nameLike 名称模糊匹配
     * @return 找到的服务数据对象列表
     */
    @Select("SELECT * FROM services WHERE name LIKE CONCAT('%', #{nameLike}, '%')")
    List<ServiceDO> findByNameLike(@Param("nameLike") String nameLike);
    
    /**
     * 根据描述模糊查询服务
     *
     * @param descriptionLike 描述模糊匹配
     * @return 找到的服务数据对象列表
     */
    @Select("SELECT * FROM services WHERE description LIKE CONCAT('%', #{descriptionLike}, '%')")
    List<ServiceDO> findByDescriptionLike(@Param("descriptionLike") String descriptionLike);

    /**
     * 批量查询服务（优化版本，使用IN查询）
     *
     * @param serviceIds 服务ID列表
     * @return 找到的服务数据对象列表
     */
    @Select("<script>SELECT * FROM services WHERE service_id IN " +
            "<foreach collection='serviceIds' item='id' open='(' separator=',' close=')'>#{id}</foreach>" +
            "</script>")
    List<ServiceDO> findByServiceIds(@Param("serviceIds") List<String> serviceIds);

    /**
     * 根据多个标签查询服务（优化版本）
     *
     * @param tags 标签列表
     * @return 找到的服务数据对象列表
     */
    @Select("<script>SELECT * FROM services WHERE " +
            "<foreach collection='tags' item='tag' separator=' AND '>" +
            "JSON_CONTAINS(tags, JSON_ARRAY(#{tag}))" +
            "</foreach>" +
            "</script>")
    List<ServiceDO> findByMultipleTags(@Param("tags") List<String> tags);

    /**
     * 统计各状态下的服务数量
     *
     * @return 状态统计结果
     */
    @Select("SELECT status, COUNT(*) as count FROM services GROUP BY status")
    List<java.util.Map<String, Object>> countByStatus();

    /**
     * 统计各类型下的服务数量
     *
     * @return 类型统计结果
     */
    @Select("SELECT type, COUNT(*) as count FROM services GROUP BY type")
    List<java.util.Map<String, Object>> countByType();

    /**
     * 获取热门服务（按更新时间排序）
     *
     * @param limit 限制数量
     * @return 热门服务列表
     */
    @Select("SELECT * FROM services WHERE status = 'ACTIVE' ORDER BY updated_at DESC LIMIT #{limit}")
    List<ServiceDO> findPopularServices(@Param("limit") int limit);

    /**
     * 全文搜索服务（名称和描述）
     *
     * @param keyword 搜索关键词
     * @param limit 限制数量
     * @param offset 偏移量
     * @return 搜索结果
     */
    @Select("SELECT * FROM services WHERE " +
            "(name LIKE CONCAT('%', #{keyword}, '%') OR description LIKE CONCAT('%', #{keyword}, '%')) " +
            "AND status = 'ACTIVE' " +
            "ORDER BY " +
            "CASE " +
            "WHEN name LIKE CONCAT('%', #{keyword}, '%') THEN 1 " +
            "WHEN description LIKE CONCAT('%', #{keyword}, '%') THEN 2 " +
            "ELSE 3 END, " +
            "updated_at DESC " +
            "LIMIT #{limit} OFFSET #{offset}")
    List<ServiceDO> searchServices(@Param("keyword") String keyword, 
                                  @Param("limit") int limit, 
                                  @Param("offset") int offset);

    /**
     * 统计搜索结果总数
     *
     * @param keyword 搜索关键词
     * @return 总数
     */
    @Select("SELECT COUNT(*) FROM services WHERE " +
            "(name LIKE CONCAT('%', #{keyword}, '%') OR description LIKE CONCAT('%', #{keyword}, '%')) " +
            "AND status = 'ACTIVE'")
    long countSearchResults(@Param("keyword") String keyword);
}