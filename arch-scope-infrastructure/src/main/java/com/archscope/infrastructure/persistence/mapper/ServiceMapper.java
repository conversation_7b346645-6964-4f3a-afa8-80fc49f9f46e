package com.archscope.infrastructure.persistence.mapper;

import com.archscope.infrastructure.persistence.entity.ServiceDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 服务Mapper接口，定义服务数据对象的数据库操作
 */
@Mapper
public interface ServiceMapper extends BaseMapper<ServiceDO> {

    /**
     * 根据服务ID查找服务
     *
     * @param serviceId 服务ID
     * @return 找到的服务数据对象
     */
    @Select("SELECT * FROM services WHERE service_id = #{serviceId} LIMIT 1")
    ServiceDO findByServiceId(@Param("serviceId") String serviceId);

    /**
     * 根据名称查找服务
     *
     * @param name 服务名称
     * @return 找到的服务数据对象
     */
    @Select("SELECT * FROM services WHERE name = #{name} LIMIT 1")
    ServiceDO findByName(@Param("name") String name);

    /**
     * 根据端点URL查找服务
     *
     * @param endpoint 服务端点URL
     * @return 找到的服务数据对象
     */
    @Select("SELECT * FROM services WHERE endpoint = #{endpoint} LIMIT 1")
    ServiceDO findByEndpoint(@Param("endpoint") String endpoint);

    /**
     * 根据Maven坐标查找服务
     *
     * @param groupId Maven/Gradle坐标 - groupId
     * @param artifactId Maven/Gradle坐标 - artifactId
     * @param version 服务版本
     * @return 找到的服务数据对象
     */
    @Select("SELECT * FROM services WHERE group_id = #{groupId} AND artifact_id = #{artifactId} AND version = #{version} LIMIT 1")
    ServiceDO findByMavenCoordinates(@Param("groupId") String groupId, @Param("artifactId") String artifactId, @Param("version") String version);

    /**
     * 根据Maven坐标查找服务列表（不指定版本）
     *
     * @param groupId Maven/Gradle坐标 - groupId
     * @param artifactId Maven/Gradle坐标 - artifactId
     * @return 找到的服务数据对象列表
     */
    @Select("SELECT * FROM services WHERE group_id = #{groupId} AND artifact_id = #{artifactId}")
    List<ServiceDO> findByMavenCoordinatesWithoutVersion(@Param("groupId") String groupId, @Param("artifactId") String artifactId);

    /**
     * 根据状态查找服务列表
     *
     * @param status 服务状态
     * @return 找到的服务数据对象列表
     */
    @Select("SELECT * FROM services WHERE status = #{status}")
    List<ServiceDO> findByStatus(@Param("status") String status);

    /**
     * 根据类型查找服务列表
     *
     * @param type 服务类型
     * @return 找到的服务数据对象列表
     */
    @Select("SELECT * FROM services WHERE type = #{type}")
    List<ServiceDO> findByType(@Param("type") String type);

    /**
     * 分页查询服务
     *
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 分页的服务数据对象列表
     */
    @Select("SELECT * FROM services LIMIT #{limit} OFFSET #{offset}")
    List<ServiceDO> findWithPagination(@Param("offset") int offset, @Param("limit") int limit);
    
    /**
     * 根据标签查找服务列表
     *
     * @param tag 标签
     * @return 找到的服务数据对象列表
     */
    @Select("SELECT * FROM services WHERE JSON_CONTAINS(tags, JSON_ARRAY(#{tag}))")
    List<ServiceDO> findByTag(@Param("tag") String tag);
    
    /**
     * 根据所有者查找服务列表
     *
     * @param owner 所有者
     * @return 找到的服务数据对象列表
     */
    @Select("SELECT * FROM services WHERE owner = #{owner}")
    List<ServiceDO> findByOwner(@Param("owner") String owner);
    
    /**
     * 查找最近注册的服务
     *
     * @param limit 限制数量
     * @return 最近注册的服务数据对象列表
     */
    @Select("SELECT * FROM services ORDER BY created_at DESC LIMIT #{limit}")
    List<ServiceDO> findRecentlyRegistered(@Param("limit") int limit);
    
    /**
     * 查找最近更新的服务
     *
     * @param limit 限制数量
     * @return 最近更新的服务数据对象列表
     */
    @Select("SELECT * FROM services ORDER BY updated_at DESC LIMIT #{limit}")
    List<ServiceDO> findRecentlyUpdated(@Param("limit") int limit);
    
    /**
     * 根据名称模糊查询服务
     *
     * @param nameLike 名称模糊匹配
     * @return 找到的服务数据对象列表
     */
    @Select("SELECT * FROM services WHERE name LIKE CONCAT('%', #{nameLike}, '%')")
    List<ServiceDO> findByNameLike(@Param("nameLike") String nameLike);
    
    /**
     * 根据描述模糊查询服务
     *
     * @param descriptionLike 描述模糊匹配
     * @return 找到的服务数据对象列表
     */
    @Select("SELECT * FROM services WHERE description LIKE CONCAT('%', #{descriptionLike}, '%')")
    List<ServiceDO> findByDescriptionLike(@Param("descriptionLike") String descriptionLike);
}