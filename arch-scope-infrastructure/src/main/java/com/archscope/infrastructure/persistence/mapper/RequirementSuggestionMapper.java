package com.archscope.infrastructure.persistence.mapper;

import com.archscope.infrastructure.persistence.entity.RequirementSuggestionDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 需求建议Mapper接口，定义需求建议数据对象的数据库操作
 */
@Mapper
public interface RequirementSuggestionMapper extends BaseMapper<RequirementSuggestionDO> {

    /**
     * 根据建议ID查找需求建议
     *
     * @param suggestionId 建议ID
     * @return 找到的需求建议数据对象
     */
    @Select("SELECT * FROM requirement_suggestions WHERE suggestion_id = #{suggestionId} LIMIT 1")
    RequirementSuggestionDO findBySuggestionId(@Param("suggestionId") String suggestionId);

    /**
     * 根据状态查找需求建议列表
     *
     * @param status 状态
     * @return 找到的需求建议数据对象列表
     */
    @Select("SELECT * FROM requirement_suggestions WHERE status = #{status}")
    List<RequirementSuggestionDO> findByStatus(@Param("status") String status);

    /**
     * 根据置信度查找需求建议列表
     *
     * @param minConfidence 最小置信度
     * @return 找到的需求建议数据对象列表
     */
    @Select("SELECT * FROM requirement_suggestions WHERE confidence >= #{minConfidence}")
    List<RequirementSuggestionDO> findByMinConfidence(@Param("minConfidence") int minConfidence);

    /**
     * 根据查询频率查找需求建议列表
     *
     * @param minQueryFrequency 最小查询频率
     * @return 找到的需求建议数据对象列表
     */
    @Select("SELECT * FROM requirement_suggestions WHERE query_frequency >= #{minQueryFrequency}")
    List<RequirementSuggestionDO> findByMinQueryFrequency(@Param("minQueryFrequency") int minQueryFrequency);

    /**
     * 根据转换后的需求ID查找需求建议
     *
     * @param convertedRequirementId 转换后的需求ID
     * @return 找到的需求建议数据对象
     */
    @Select("SELECT * FROM requirement_suggestions WHERE converted_requirement_id = #{convertedRequirementId} LIMIT 1")
    RequirementSuggestionDO findByConvertedRequirementId(@Param("convertedRequirementId") String convertedRequirementId);

    /**
     * 查找最近创建的需求建议
     *
     * @param limit 限制数量
     * @return 最近创建的需求建议数据对象列表
     */
    @Select("SELECT * FROM requirement_suggestions ORDER BY created_at DESC LIMIT #{limit}")
    List<RequirementSuggestionDO> findRecentlyCreated(@Param("limit") int limit);

    /**
     * 查找最近更新的需求建议
     *
     * @param limit 限制数量
     * @return 最近更新的需求建议数据对象列表
     */
    @Select("SELECT * FROM requirement_suggestions ORDER BY updated_at DESC LIMIT #{limit}")
    List<RequirementSuggestionDO> findRecentlyUpdated(@Param("limit") int limit);

    /**
     * 更新需求建议的状态
     *
     * @param suggestionId 建议ID
     * @param status 状态
     * @return 影响的行数
     */
    @Update("UPDATE requirement_suggestions SET status = #{status} WHERE suggestion_id = #{suggestionId}")
    int updateStatus(@Param("suggestionId") String suggestionId, @Param("status") String status);

    /**
     * 更新需求建议的转换状态
     *
     * @param suggestionId 建议ID
     * @param status 状态
     * @param convertedRequirementId 转换后的需求ID
     * @return 影响的行数
     */
    @Update("UPDATE requirement_suggestions SET status = #{status}, converted_requirement_id = #{convertedRequirementId} WHERE suggestion_id = #{suggestionId}")
    int updateConversionStatus(@Param("suggestionId") String suggestionId, @Param("status") String status, @Param("convertedRequirementId") String convertedRequirementId);

    /**
     * 增加需求建议的查询频率
     *
     * @param suggestionId 建议ID
     * @param increment 增量
     * @return 影响的行数
     */
    @Update("UPDATE requirement_suggestions SET query_frequency = query_frequency + #{increment} WHERE suggestion_id = #{suggestionId}")
    int incrementQueryFrequency(@Param("suggestionId") String suggestionId, @Param("increment") int increment);

    /**
     * 分页查询需求建议
     *
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 分页的需求建议数据对象列表
     */
    @Select("SELECT * FROM requirement_suggestions ORDER BY confidence DESC, query_frequency DESC LIMIT #{limit} OFFSET #{offset}")
    List<RequirementSuggestionDO> findWithPagination(@Param("offset") int offset, @Param("limit") int limit);
}