package com.archscope.infrastructure.persistence.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 需求建议数据对象，对应数据库中的requirement_suggestions表
 */
@Data
@TableName("requirement_suggestions")
public class RequirementSuggestionDO {

    /**
     * 建议唯一标识符 (UUID)
     */
    @TableId("suggestion_id")
    private String suggestionId;

    /**
     * 建议标题
     */
    private String title;

    /**
     * 建议描述
     */
    private String description;

    /**
     * 建议优先级: HIGH, MEDIUM, LOW
     */
    private String suggestedPriority;

    /**
     * 置信度 (0-100)
     */
    private Integer confidence;

    /**
     * 相关查询频率
     */
    private Integer queryFrequency;

    /**
     * 状态: OPEN, CONVERTED, DISMISSED
     */
    private String status;

    /**
     * 转换后的需求ID
     */
    private String convertedRequirementId;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}