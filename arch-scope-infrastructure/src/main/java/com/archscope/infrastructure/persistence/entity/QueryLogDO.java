package com.archscope.infrastructure.persistence.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 查询日志数据对象，对应数据库中的query_logs表
 */
@Data
@TableName(value = "query_logs", autoResultMap = true)
public class QueryLogDO {

    /**
     * 查询日志唯一标识符 (UUID)
     */
    @TableId("query_log_id")
    private String queryLogId;

    /**
     * 查询类型: SERVICE_DISCOVERY, CAPABILITY_QUERY
     */
    private String queryType;

    /**
     * 查询参数 (JSON对象)
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> queryParams;

    /**
     * 结果数量
     */
    private Integer resultCount;

    /**
     * 是否有结果
     */
    private Boolean hasResults;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 用户反馈: SATISFIED, UNSATISFIED, NONE
     */
    private String userFeedback;

    /**
     * 是否已转换为需求
     */
    private Boolean convertedToRequirement;

    /**
     * 关联的需求ID
     */
    private String relatedRequirementId;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
}