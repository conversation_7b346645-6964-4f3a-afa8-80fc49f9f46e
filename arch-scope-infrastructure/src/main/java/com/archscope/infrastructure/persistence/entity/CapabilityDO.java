package com.archscope.infrastructure.persistence.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 能力数据对象，对应数据库中的capabilities表
 */
@Data
@TableName(value = "capabilities", autoResultMap = true)
public class CapabilityDO {

    /**
     * 能力唯一标识符 (UUID)
     */
    @TableId("capability_id")
    private String capabilityId;

    /**
     * 关联的服务ID
     */
    private String serviceId;

    /**
     * 能力名称
     */
    private String name;

    /**
     * 能力描述
     */
    private String description;

    /**
     * 函数签名
     */
    private String functionSignature;

    /**
     * 输入参数JSON Schema
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> inputSchema;

    /**
     * 输出参数JSON Schema
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> outputSchema;

    /**
     * 示例 (JSON数组)
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Map<String, Object>> examples;

    /**
     * 能力标签 (JSON数组)
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> tags;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}