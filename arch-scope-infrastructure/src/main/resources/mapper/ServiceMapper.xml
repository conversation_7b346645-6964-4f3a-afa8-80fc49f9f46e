<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.archscope.infrastructure.persistence.mapper.ServiceMapper">

    <!-- 基本结果映射 -->
    <resultMap id="BaseResultMap" type="com.archscope.infrastructure.persistence.entity.ServiceDO">
        <id column="service_id" property="serviceId" />
        <result column="name" property="name" />
        <result column="description" property="description" />
        <result column="version" property="version" />
        <result column="type" property="type" />
        <result column="endpoint" property="endpoint" />
        <result column="group_id" property="groupId" />
        <result column="artifact_id" property="artifactId" />
        <result column="owner" property="owner" />
        <result column="api_doc_url" property="apiDocUrl" />
        <result column="status" property="status" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 带JSON字段的结果映射 -->
    <resultMap id="FullResultMap" type="com.archscope.infrastructure.persistence.entity.ServiceDO" extends="BaseResultMap">
        <result column="tags" property="tags" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler" />
        <result column="metadata" property="metadata" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler" />
    </resultMap>

    <!-- 复杂查询：根据多个条件查询服务 -->
    <select id="findServicesByMultipleCriteria" resultMap="FullResultMap">
        SELECT * FROM services
        <where>
            <if test="name != null and name != ''">
                AND name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="type != null and type != ''">
                AND type = #{type}
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <if test="groupId != null and groupId != ''">
                AND group_id = #{groupId}
            </if>
            <if test="artifactId != null and artifactId != ''">
                AND artifact_id = #{artifactId}
            </if>
            <if test="version != null and version != ''">
                AND version = #{version}
            </if>
            <if test="owner != null and owner != ''">
                AND owner = #{owner}
            </if>
            <if test="tag != null and tag != ''">
                AND JSON_CONTAINS(tags, JSON_ARRAY(#{tag}))
            </if>
        </where>
        ORDER BY 
        <choose>
            <when test="orderBy != null and orderBy != ''">
                ${orderBy}
            </when>
            <otherwise>
                created_at DESC
            </otherwise>
        </choose>
        <if test="limit > 0">
            LIMIT #{limit}
            <if test="offset >= 0">
                OFFSET #{offset}
            </if>
        </if>
    </select>

    <!-- 复杂查询：根据能力名称查询提供该能力的服务 -->
    <select id="findServicesByCapabilityName" resultMap="FullResultMap">
        SELECT s.* FROM services s
        INNER JOIN capabilities c ON s.service_id = c.service_id
        WHERE c.name = #{capabilityName}
        ORDER BY s.created_at DESC
    </select>

    <!-- 复杂查询：根据多个标签查询服务 -->
    <select id="findServicesByTags" resultMap="FullResultMap">
        SELECT * FROM services
        <where>
            <foreach collection="tags" item="tag" separator=" AND ">
                JSON_CONTAINS(tags, JSON_ARRAY(#{tag}))
            </foreach>
        </where>
        ORDER BY created_at DESC
    </select>

    <!-- 复杂查询：根据描述关键词查询服务 -->
    <select id="findServicesByDescriptionKeywords" resultMap="FullResultMap">
        SELECT * FROM services
        <where>
            <foreach collection="keywords" item="keyword" separator=" OR ">
                description LIKE CONCAT('%', #{keyword}, '%')
            </foreach>
        </where>
        ORDER BY created_at DESC
    </select>

    <!-- 复杂查询：查询最近更新的服务，按类型分组 -->
    <select id="findRecentlyUpdatedServicesByType" resultMap="FullResultMap">
        SELECT * FROM (
            SELECT *, ROW_NUMBER() OVER (PARTITION BY type ORDER BY updated_at DESC) as row_num
            FROM services
        ) ranked
        WHERE row_num <= #{limitPerType}
        ORDER BY type, updated_at DESC
    </select>

    <!-- 复杂查询：查询服务数量统计 -->
    <select id="countServicesByStatus" resultType="java.util.Map">
        SELECT status, COUNT(*) as count
        FROM services
        GROUP BY status
    </select>
</mapper>