<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.archscope.infrastructure.persistence.mapper.QueryLogMapper">

    <!-- 基本结果映射 -->
    <resultMap id="BaseResultMap" type="com.archscope.infrastructure.persistence.entity.QueryLogDO">
        <id column="query_log_id" property="queryLogId" />
        <result column="query_type" property="queryType" />
        <result column="result_count" property="resultCount" />
        <result column="has_results" property="hasResults" />
        <result column="session_id" property="sessionId" />
        <result column="user_id" property="userId" />
        <result column="user_feedback" property="userFeedback" />
        <result column="converted_to_requirement" property="convertedToRequirement" />
        <result column="related_requirement_id" property="relatedRequirementId" />
        <result column="created_at" property="createdAt" />
    </resultMap>

    <!-- 带JSON字段的结果映射 -->
    <resultMap id="FullResultMap" type="com.archscope.infrastructure.persistence.entity.QueryLogDO" extends="BaseResultMap">
        <result column="query_params" property="queryParams" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler" />
    </resultMap>

    <!-- 复杂查询：根据多个条件查询查询日志 -->
    <select id="findQueryLogsByMultipleCriteria" resultMap="FullResultMap">
        SELECT * FROM query_logs
        <where>
            <if test="queryType != null and queryType != ''">
                AND query_type = #{queryType}
            </if>
            <if test="hasResults != null">
                AND has_results = #{hasResults}
            </if>
            <if test="sessionId != null and sessionId != ''">
                AND session_id = #{sessionId}
            </if>
            <if test="userId != null and userId != ''">
                AND user_id = #{userId}
            </if>
            <if test="userFeedback != null and userFeedback != ''">
                AND user_feedback = #{userFeedback}
            </if>
            <if test="convertedToRequirement != null">
                AND converted_to_requirement = #{convertedToRequirement}
            </if>
            <if test="relatedRequirementId != null and relatedRequirementId != ''">
                AND related_requirement_id = #{relatedRequirementId}
            </if>
            <if test="startDate != null">
                AND created_at >= #{startDate}
            </if>
            <if test="endDate != null">
                AND created_at <= #{endDate}
            </if>
        </where>
        ORDER BY 
        <choose>
            <when test="orderBy != null and orderBy != ''">
                ${orderBy}
            </when>
            <otherwise>
                created_at DESC
            </otherwise>
        </choose>
        <if test="limit > 0">
            LIMIT #{limit}
            <if test="offset >= 0">
                OFFSET #{offset}
            </if>
        </if>
    </select>

    <!-- 复杂查询：查询查询日志及其关联的需求信息 -->
    <select id="findQueryLogsWithRequirementInfo" resultType="java.util.Map">
        SELECT ql.*, r.title as requirement_title, r.status as requirement_status
        FROM query_logs ql
        LEFT JOIN requirements r ON ql.related_requirement_id = r.requirement_id
        <where>
            <if test="convertedToRequirement != null">
                AND ql.converted_to_requirement = #{convertedToRequirement}
            </if>
        </where>
        ORDER BY ql.created_at DESC
        <if test="limit > 0">
            LIMIT #{limit}
            <if test="offset >= 0">
                OFFSET #{offset}
            </if>
        </if>
    </select>

    <!-- 复杂查询：查询查询日志及其关联的建议信息 -->
    <select id="findQueryLogsWithSuggestionInfo" resultType="java.util.Map">
        SELECT ql.*, rs.title as suggestion_title, rs.status as suggestion_status, qlsr.relevance_score
        FROM query_logs ql
        INNER JOIN query_log_suggestion_relations qlsr ON ql.query_log_id = qlsr.query_log_id
        INNER JOIN requirement_suggestions rs ON qlsr.suggestion_id = rs.suggestion_id
        <where>
            <if test="minRelevanceScore != null">
                AND qlsr.relevance_score >= #{minRelevanceScore}
            </if>
        </where>
        ORDER BY qlsr.relevance_score DESC, ql.created_at DESC
        <if test="limit > 0">
            LIMIT #{limit}
            <if test="offset >= 0">
                OFFSET #{offset}
            </if>
        </if>
    </select>

    <!-- 复杂查询：查询查询日志统计信息 -->
    <select id="getQueryLogStatistics" resultType="java.util.Map">
        SELECT 
            query_type,
            COUNT(*) as total_queries,
            SUM(CASE WHEN has_results = 1 THEN 1 ELSE 0 END) as queries_with_results,
            SUM(CASE WHEN has_results = 0 THEN 1 ELSE 0 END) as queries_without_results,
            AVG(result_count) as avg_result_count,
            SUM(CASE WHEN user_feedback = 'SATISFIED' THEN 1 ELSE 0 END) as satisfied_users,
            SUM(CASE WHEN user_feedback = 'UNSATISFIED' THEN 1 ELSE 0 END) as unsatisfied_users,
            SUM(CASE WHEN converted_to_requirement = 1 THEN 1 ELSE 0 END) as converted_to_requirements
        FROM query_logs
        <where>
            <if test="startDate != null">
                AND created_at >= #{startDate}
            </if>
            <if test="endDate != null">
                AND created_at <= #{endDate}
            </if>
        </where>
        GROUP BY query_type
    </select>
</mapper>