package com.archscope.app.util;

import com.archscope.facade.dto.ApiResponse;
import org.springframework.http.ResponseEntity;

/**
 * 响应工具类，统一处理API响应格式
 */
public class ResponseHelper {
    
    public static <T> ResponseEntity<ApiResponse<T>> success(String message, T data) {
        return ResponseEntity.ok(ApiResponse.success(message, data));
    }
    
    public static <T> ResponseEntity<ApiResponse<T>> success(T data) {
        return ResponseEntity.ok(ApiResponse.success("操作成功", data));
    }
    
    public static <T> ResponseEntity<ApiResponse<T>> notFound(String message) {
        return ResponseEntity.ok(ApiResponse.success(message, null));
    }
}