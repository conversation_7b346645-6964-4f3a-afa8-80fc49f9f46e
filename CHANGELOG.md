# 更新日志

本文件记录了ArchScope项目的所有重要变更。

格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
并且本项目遵循 [语义化版本](https://semver.org/lang/zh-CN/)。

## [未发布]

### 修复
- 修复了Spring Boot应用启动时的数据库字段类型错误
  - 问题：Project实体的documentationVersion字段期望Integer类型，但数据库中存储了字符串"demo-v1.0.0"
  - 解决：将数据库中的错误数据修正为数字1
  - 影响：解决了`NumberFormatException: For input string: "demo-v1.0.0"`错误

### 改进
- 更新了README.md文档，添加了完整的前端和后端运行指南
  - 添加了详细的环境要求和安装步骤
  - 包含了Docker Compose启动数据库服务的说明
  - 添加了故障排除部分，记录常见问题和解决方案
  - 提供了验证安装的测试命令

### 技术细节
- 成功解决了磁盘空间不足导致的编译失败问题
- 确认了应用程序的容错机制正常工作（RocketMQ不可用时的优雅降级）
- 验证了MySQL、Redis连接正常
- 确认了前后端通信正常

## [0.0.1] - 2025-07-17

### 新增
- 初始项目结构
- DDD六边形架构实现
- 前端Vue3 + TypeScript应用
- 后端Spring Boot应用
- 数据库迁移脚本
- Docker Compose配置
